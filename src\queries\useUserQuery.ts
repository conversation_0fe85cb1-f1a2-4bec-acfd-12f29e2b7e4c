import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";


// Query keys for better cache management
export const queryKeys = {
  users: {
    all: ['users'] as const,
    detail: (id: string) => [...queryKeys.users.all, 'detail', id] as const,
  },
};


// User-related hooks
export const useUserDetails = (userId: string, enabled = true) => {
  return useQuery({
    queryKey: [queryKeys.users.detail(userId)],
    queryFn: () => {
      return supabase
        .from('user_details')
        .select('*')
        .eq('id', userId)
        .single();
    },
    enabled: enabled && !!userId, // Only run if userId exists and enabled is true
    staleTime: 5 * 60 * 1000, // 5 minutes
    //cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

