import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface Review {
  id: string;
  user_id: string;
  business_id: string;
  booking_id: string;
  rating: number;
  comment: string | null;
  created_at: string;
  updated_at: string;
}

export interface ReviewFormData {
  rating: number;
  comment: string;
  booking_id: string;
  business_id: string;
}

// Hook to get reviews for a business
export const useBusinessReviews = (businessId: string) => {
  return useQuery({
    queryKey: ['business-reviews', businessId],
    queryFn: async (): Promise<Review[]> => {
      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    },
    enabled: !!businessId,
 //   staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Hook to create a review
export const useCreateReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reviewData: ReviewFormData) => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      // First, analyze the review for moderation and sentiment
      const { data: analysisData, error: analysisError } = await supabase.functions.invoke('analyze-review', {
        body: {
          comment: reviewData.comment.trim(),
          rating: reviewData.rating
        }
      });

      if (analysisError) {
        throw new Error('Errore durante l\'analisi della recensione: ' + analysisError.message);
      }

      if (analysisData.blocked) {
        throw new Error(analysisData.error);
      }

      // If analysis passed, insert the review with sentiment
      const { data, error } = await supabase
        .from('reviews')
        .insert({
          user_id: user.id,
          business_id: reviewData.business_id,
          booking_id: reviewData.booking_id,
          rating: reviewData.rating,
          comment: reviewData.comment.trim() || null,
          sentiment: analysisData.sentiment
        })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      // Update business score and review count
      await updateBusinessStats(reviewData.business_id);

      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['business-reviews', variables.business_id] });
      queryClient.invalidateQueries({ queryKey: ['businesses'] });
      toast.success("Recensione creata con successo!");
    },
    onError: (error: Error) => {
      console.error('Error creating review:', error);
      toast.error("Errore nella creazione della recensione: " + error.message);
    },
  });
};

// Helper function to update business statistics
const updateBusinessStats = async (businessId: string) => {
  // Get all reviews for this business
  const { data: reviews, error: reviewsError } = await supabase
    .from('reviews')
    .select('rating')
    .eq('business_id', businessId);

  if (reviewsError) {
    console.error('Error fetching reviews for business stats:', reviewsError);
    return;
  }

  const reviewCount = reviews.length;
  const averageScore = reviewCount > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviewCount 
    : 0;

  // Update business with new stats
  const { error: updateError } = await supabase
    .from('businesses')
    .update({
      score: Math.round(averageScore * 100) / 100, // Round to 2 decimal places
      review_count: reviewCount
    })
    .eq('id', businessId);

  if (updateError) {
    console.error('Error updating business stats:', updateError);
  }
};

// Hook to get user's review for a specific booking
export const useUserBookingReview = (bookingId: string) => {
  return useQuery({
    queryKey: ['user-booking-review', bookingId],
    queryFn: async (): Promise<Review | null> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('reviews')
        .select('*')
        .eq('booking_id', bookingId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    enabled: !!bookingId,
  });
};

// Hook to check if user can review a booking
export const useCanReviewBooking = (bookingId: string) => {
  return useQuery({
    queryKey: ['can-review-booking', bookingId],
    queryFn: async (): Promise<boolean> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return false;
      }

      // Check if booking is completed and belongs to user
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .select('status, user_id')
        .eq('id', bookingId)
        .eq('user_id', user.id)
        .single();

      if (bookingError || !booking) {
        return false;
      }

      // if (booking.status !== 'completed') {
      //   return false;
      // }

      // Check if user hasn't already reviewed this booking
      const { data: existingReview, error: reviewError } = await supabase
        .from('reviews')
        .select('id')
        .eq('booking_id', bookingId)
        .eq('user_id', user.id)
        .maybeSingle();

      if (reviewError) {
        return false;
      }

      return !existingReview;
    },
    enabled: !!bookingId,
  });
};