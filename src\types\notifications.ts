export interface VoiceAINotification {
  id: string;
  entityId?: string;
  message: string;
  metadata:  'deal' | 'booking' | 'message' | 'searching' | 'end_search' | 'start_search' | 'action' | 'action_done' | 'action_error' | 'query';
  timestamp: number;
}





export interface Notification {
  id: string;
  entity: string;
  entity_id: string;
  created_at: string;
  user_id?: string | null;
  updated_at?: string | null;
  read_at?: string | null;
}

