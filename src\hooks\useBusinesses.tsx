
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface Business {
  id: string;
  name: string;
  address: string;
  formatted_address?: string;
  zip_code: string;
  city: string;
  state: string;
  country: string;
  description: string | null;
  created_at: string;
  updated_at: string | null;
  latitude: number | null;
  longitude: number | null;
  photos: string[] | null;
  owner_id: string;
  category_id: string | null;
  email?: string | null;
  phone?: string | null;
  website?: string | null;
  deal_count?: number | null;
  booking_count?: number | null;
  pending_booking_count?: number | null;
  score?: number | null;
  review_count?: number | null;
}

export const useBusinesses = (userId?: string, enable = true) => {
  const {
    data: userBusinesses,
    isLoading,
    error: queryError,
    refetch: refreshBusinesses,
  } = useQuery({
    queryKey: ['businesses', userId],
    queryFn: async (): Promise<Business[]> => {
      if (!userId) {
        return [];
      }

      const { data, error: fetchError } = await supabase
        .from('businesses')
        .select(`
          id, name, address, description, created_at, updated_at, 
          latitude, longitude, photos, owner_id, category_id,
          email, phone, website, score, review_count
        `)
        .eq('owner_id', userId);

      if (fetchError) {
        throw new Error(`Errore nel caricamento delle attività: ${fetchError.message}`);
      }

      return (data as Business[]) || [];
    },
    enabled: !!userId && enable,
    staleTime: 1000 * 60 * 5, // 5 minutes - business data changes moderately
    refetchOnWindowFocus: false,
  });

  // Convert React Query error to Error type for compatibility
  const error = queryError ? new Error(queryError.message || 'Unknown error') : null;

  return {
    userBusinesses: userBusinesses || [],
    isLoading,
    error,
    refreshBusinesses
  };
};

export const useBusinessById = (businessId: string) => {
  const {
    data: business,
    isLoading,
    error: queryError,
  } = useQuery({
    queryKey: ['business', businessId],
    queryFn: async (): Promise<Business | null> => {
      const { data, error: fetchError } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', businessId)
        .single();
      
      if (fetchError) {
        throw new Error(fetchError.message);
      }
      
      return data as Business || null;
    },
    enabled: !!businessId,
   // staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });

  // Convert React Query error to Error type for compatibility
  const error = queryError ? new Error(queryError.message || 'Unknown error') : null;

  return {
    business: business || null,
    isLoading,
    error,
  };
};
