import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { Composio } from "https://esm.sh/@composio/core@0.1.39"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ComposioRequest {
  action: 'connect' | 'callback' | 'check_status' | 'fetch_emails' | 'send_email' | 'disconnect'
  userId?: string
  authConfigId?: string
  code?: string
  state?: string
  connectionId?: string
  to?: string
  subject?: string
  body?: string
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get auth header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Verify user
    const { data: { user }, error: authError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))
    if (authError || !user) {
      throw new Error('Invalid user token')
    }

    const { action, ...params }: ComposioRequest = await req.json()

    // Initialize Composio
    const composioApiKey = Deno.env.get('COMPOSIO_API_KEY')
    if (!composioApiKey) {
      throw new Error('Missing COMPOSIO_API_KEY')
    }

    const composio = new Composio({ apiKey: composioApiKey })

    let result

    switch (action) {
      case 'connect': {
        const { userId, authConfigId } = params
        try {
          // Initiate OAuth flow
          const response = await composio.connectedAccounts.initiate(
            userId || user.id,
            authConfigId || "ac_UvsrdfxJ2h2X" // Gmail auth config ID
          )

          result = {
            success: true,
            data: {
              connectionId: response.id,
              redirectUrl: response.redirectUrl
            }
          }
        } catch (e) {
          const msg = (e as Error).message || ''
          // If account already exists for this user+auth config, try to reuse it
          if (msg.includes('Multiple connected accounts')) {
            try {
              const anyApi: any = composio as any
              const listFn = anyApi?.connectedAccounts?.list
              let existing: any = null
              if (typeof listFn === 'function') {
                const list = await listFn({ userId: userId || user.id, authConfigId: authConfigId || "ac_UvsrdfxJ2h2X" })
                existing = Array.isArray(list) ? list.find((acc: any) => (acc?.provider || acc?.app)?.toString().toLowerCase().includes('gmail')) : null
              }
              if (existing?.id) {
                result = { success: true, data: { connectionId: existing.id, redirectUrl: null } }
              } else {
                throw e
              }
            } catch (inner) {
              throw new Error(`connect fallback failed: ${(inner as Error).message}`)
            }
          } else {
            throw e
          }
        }
        break
      }

      case 'callback': {
        const { connectionId, code, state } = params
        
        try {
          // Confirm connection exists and fetch details
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)

          const scopeVal = connectedAccount?.connectionParams?.scope
          const scopes = Array.isArray(scopeVal) ? scopeVal : (scopeVal?.split?.(' ') ?? [])
          const email = connectedAccount?.connectionParams?.email ?? null
          const status = (connectedAccount?.status || '').toString().toUpperCase()

          // Persist authorization (business_id removed)
          const { error: dbError } = await supabase
            .from('integration_authorizations')
            .upsert({
              user_id: user.id,
              provider: 'gmail',
              integration_id: connectionId!,
              status: status || 'connected',
              is_active: true,
              access_token: 'pending',
              metadata: {
                email,
                scopes,
                authorized_at: new Date().toISOString(),
                last_status: status
              }
            }, { onConflict: 'user_id,provider' })

          if (dbError) throw dbError

          result = {
            success: true,
            data: { connected: true }
          }
        } catch (error) {
          throw new Error(`Callback failed: ${error.message}`)
        }
        break
      }

      case 'check_status': {
        const { connectionId, authConfigId } = params
        
        try {
          // Seed a row when we have a connectionId so the UI/DB reflects the link
          if (connectionId) {
            const { error: seedErr } = await supabase
              .from('integration_authorizations')
              .upsert({
                user_id: user.id,
                provider: 'gmail',
                integration_id: connectionId,
                status: 'connected',
                is_active: true,
                access_token: 'pending',
                metadata: { seeded_at: new Date().toISOString() }
              }, { onConflict: 'user_id,provider' })
            if (seedErr) console.log('seed upsert warning:', seedErr.message)
          }

          let connectedAccount: any = null
          try {
            connectedAccount = await composio.connectedAccounts.get(connectionId!)
          } catch (_) {
            // ignore get errors, we'll fallback to list
          }

          const normalize = (acc: any) => {
            const status = (acc?.status || '').toString().toUpperCase()
            const email = acc?.connectionParams?.email ?? null
            const scopeVal = acc?.connectionParams?.scope
            const scopes = Array.isArray(scopeVal) ? scopeVal : (scopeVal?.split?.(' ') ?? [])
            const consideredConnected = ['CONNECTED', 'AUTHORIZED', 'VERIFIED'].includes(status) || !!email
            return { status, email, scopes, consideredConnected }
          }

          let { status, email, scopes, consideredConnected } = normalize(connectedAccount)

          // Fallback: list accounts for this user+authConfig and pick a connected Gmail
          if (!consideredConnected) {
            try {
              const anyApi: any = composio as any
              const listFn = anyApi?.connectedAccounts?.list
              if (typeof listFn === 'function') {
                const list = await listFn({ userId: user.id, authConfigId: authConfigId || 'ac_UvsrdfxJ2h2X' })
                const match = (Array.isArray(list) ? list : []).find((acc: any) => {
                  const s = (acc?.status || '').toString().toUpperCase()
                  const hasEmail = !!acc?.connectionParams?.email
                  const isConnected = ['CONNECTED', 'AUTHORIZED', 'VERIFIED'].includes(s)
                  const isGmail = ((acc?.provider || acc?.app || '') + '').toLowerCase().includes('gmail')
                  return isGmail && (isConnected || hasEmail)
                })
                if (match) {
                  connectedAccount = match
                  const n = normalize(match)
                  status = n.status; email = n.email; scopes = n.scopes; consideredConnected = n.consideredConnected
                }
              }
            } catch (_) { /* ignore list errors */ }
          }

          const hasAccount = !!connectedAccount?.id
          if (hasAccount) {
            const { error: dbError } = await supabase
              .from('integration_authorizations')
              .upsert({
                user_id: user.id,
                provider: 'gmail',
                integration_id: connectedAccount.id,
                status: status || 'connected',
                is_active: true,
                access_token: 'pending',
                metadata: {
                  email,
                  scopes,
                  last_status: status,
                  last_checked_at: new Date().toISOString()
                }
              }, { onConflict: 'user_id,provider' })
            if (dbError) throw dbError
          }

          result = { success: true, data: { connected: consideredConnected } }
        } catch (error) {
          throw new Error(`Status check failed: ${error.message}`)
        }
        break
      }

      case 'fetch_emails': {
        const { connectionId } = params
        
        try {
          // Get connected account
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)
          
          // Use the connected account to fetch emails via Gmail API
          // For now, return mock data - you'd implement actual Gmail API calls here
          const emails = [
            {
              id: 'demo_email_1',
              sender: '<EMAIL>',
              subject: 'Benvenuto in Gmail!',
              preview: 'Grazie per aver connesso il tuo account Gmail con CatchUp...',
              time: new Date().toLocaleDateString('it-IT'),
              isRead: false,
              isStarred: false,
              body: 'Messaggio di benvenuto completo qui...'
            }
          ]

          result = {
            success: true,
            data: { emails }
          }
        } catch (error) {
          throw new Error(`Failed to fetch emails: ${error.message}`)
        }
        break
      }

      case 'send_email': {
        const { connectionId, to, subject, body } = params
        
        try {
          // Get connected account
          const connectedAccount = await composio.connectedAccounts.get(connectionId!)
          
          // Send email via Gmail API through Composio
          // For now, simulate successful send
          console.log(`Simulated sending email to: ${to}, subject: ${subject}`)

          result = {
            success: true,
            data: { messageId: `msg_${Date.now()}` }
          }
        } catch (error) {
          throw new Error(`Failed to send email: ${error.message}`)
        }
        break
      }

      case 'disconnect': {
        const { connectionId } = params
        
        try {
          // Deactivate in database
          const { error: dbError } = await supabase
            .from('integration_authorizations')
            .update({ is_active: false })
            .eq('user_id', user.id)
            .eq('provider', 'gmail')

          if (dbError) throw dbError

          result = {
            success: true,
            data: { disconnected: true }
          }
        } catch (error) {
          throw new Error(`Failed to disconnect: ${error.message}`)
        }
        break
      }

      default:
        throw new Error(`Unknown action: ${action}`)
    }

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Composio Gmail error:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})