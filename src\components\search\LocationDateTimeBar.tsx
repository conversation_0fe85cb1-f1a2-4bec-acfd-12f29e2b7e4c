import React, { useState, useCallback, FormEvent } from "react";
import { MapPin, Calendar, Clock, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { useAutocompleteSuggestions } from "@/hooks/useAutocompleteSuggestions";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { DateRange } from "react-day-picker";
import { format } from "date-fns";
import { it } from "date-fns/locale";

interface LocationDateTimeBarProps {
  location?: string;
  date?: string;
  time?: string;
  onLocationChange?: (location: string, place?: google.maps.places.Place) => void;
  onDateChange?: (
    value:
      | { mode: "single"; date: string }
      | { mode: "range"; startDate: string; endDate: string }
  ) => void;
  onTimeChange?: (
    value:
      | { mode: "single"; time: string }
      | { mode: "range"; startTime: string; endTime: string }
  ) => void;
}

export const LocationDateTimeBar: React.FC<LocationDateTimeBarProps> = ({
  location = "Posizione Attuale",
  date,
  time,
  onLocationChange,
  onDateChange,
  onTimeChange,
}) => {
  const places = useMapsLibrary('places');
  const [isSearching, setIsSearching] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const { suggestions, resetSession } = useAutocompleteSuggestions(searchValue);

  // Date state
  const [isDateOpen, setIsDateOpen] = useState(false);
  const [dateMode, setDateMode] = useState<"single" | "range">("single");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    date ? new Date(date) : undefined
  );
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);

  // Time state
  const [isTimeOpen, setIsTimeOpen] = useState(false);
  const [timeMode, setTimeMode] = useState<"single" | "range">("single");
  const [selectedTime, setSelectedTime] = useState<string>(time || "");
  const [timeRange, setTimeRange] = useState<{ start?: string; end?: string }>({});

  const handleInput = useCallback((event: FormEvent<HTMLInputElement>) => {
    setSearchValue((event.target as HTMLInputElement).value);
  }, []);

  const handleSuggestionClick = useCallback(
    async (suggestion: google.maps.places.AutocompleteSuggestion) => {
      if (!places) return;
      if (!suggestion.placePrediction) return;

      const place = suggestion.placePrediction.toPlace();

      await place.fetchFields({
        fields: ['viewport', 'location', 'displayName', 'formattedAddress'],
      });

      const locationName = suggestion.placePrediction.text.text;
      console.log("Selezione location:", locationName);
      onLocationChange?.(locationName, place);
      setSearchValue('');
      setIsSearching(false);
      resetSession();
    },
    [places, onLocationChange, resetSession]
  );
  return (
    <div className="fixed top-20 left-4 right-4 z-50 bg-white rounded-full shadow-lg border border-gray-200 transition-all duration-300 ease-in-out">
      <div className="flex items-center divide-x divide-gray-200 transition-all duration-300 ease-in-out">
        {/* Location Section */}
        <div className={`flex items-center gap-2 px-4 py-3 min-w-0 ${isSearching ? "flex-[2] flex-grow" : "flex-1"}`}>
          <MapPin className="h-5 w-5 text-gray-400 flex-shrink-0" />
          {isSearching ? (
            <div className="relative flex-1">
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  placeholder="Cerca una posizione..."
                  value={searchValue}
                  onInput={handleInput}
                  className="h-8 text-sm border-none p-0 focus-visible:ring-0 w-full flex-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      setIsSearching(false);
                      setSearchValue("");
                    }
                  }}
                  autoFocus
                />
                <Button
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={() => {
                    setIsSearching(false);
                    setSearchValue("");
                  }}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Suggestions dropdown */}
              {suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      <div className="font-medium text-gray-900">
                        {suggestion.placePrediction?.text?.text}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <span 
              className="text-sm font-medium text-gray-900 truncate cursor-pointer hover:text-primary"
              onClick={() => setIsSearching(true)}
            >
              {location}
            </span>
          )}
        </div>

        {!isSearching && (
          <>
            {/* Date Section */}
            <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
              <PopoverTrigger asChild>
                <button className="flex items-center gap-2 px-4 py-3 transition-all duration-300 ease-in-out hover:bg-gray-50">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">
                    {dateMode === "single"
                      ? selectedDate
                        ? format(selectedDate, "dd MMM", { locale: it })
                        : "Data"
                      : dateRange?.from && dateRange?.to
                        ? `${format(dateRange.from, "dd MMM", { locale: it })} - ${format(dateRange.to, "dd MMM", { locale: it })}`
                        : "Intervallo date"}
                  </span>
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-3" align="center">
                <div className="space-y-3">
                  <ToggleGroup type="single" value={dateMode} onValueChange={(v) => { if (v === "single" || v === "range") setDateMode(v); }}>
                    <ToggleGroupItem value="single" size="sm">Singola</ToggleGroupItem>
                    <ToggleGroupItem value="range" size="sm">Intervallo</ToggleGroupItem>
                  </ToggleGroup>
                  {dateMode === "single" ? (
                    <CalendarComponent
                      mode="single"
                      selected={selectedDate}
                      onSelect={(d?: Date) => {
                        if (d) {
                          setSelectedDate(d);
                          const ds = format(d, "yyyy-MM-dd");
                          onDateChange?.({ mode: "single", date: ds });
                          setIsDateOpen(false);
                        }
                      }}
                      initialFocus
                      className="pointer-events-auto"
                      locale={it}
                    />
                  ) : (
                    <CalendarComponent
                      mode="range"
                      selected={dateRange}
                      onSelect={(r?: DateRange) => {
                        if (r?.from && r?.to) {
                          setDateRange(r);
                          onDateChange?.({
                            mode: "range",
                            startDate: format(r.from, "yyyy-MM-dd"),
                            endDate: format(r.to, "yyyy-MM-dd"),
                          });
                          setIsDateOpen(false);
                        } else {
                          setDateRange(r);
                        }
                      }}
                      initialFocus
                      className="pointer-events-auto"
                      locale={it}
                    />
                  )}
                </div>
              </PopoverContent>
            </Popover>

            {/* Time Section */}
            <Popover open={isTimeOpen} onOpenChange={setIsTimeOpen}>
              <PopoverTrigger asChild>
                <button className="flex items-center gap-2 px-4 py-3 transition-all duration-300 ease-in-out hover:bg-gray-50">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">
                    {timeMode === "single"
                      ? selectedTime || "Ora"
                      : timeRange.start && timeRange.end
                        ? `${timeRange.start} - ${timeRange.end}`
                        : "Intervallo orario"}
                  </span>
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-[260px] p-3" align="center">
                <div className="space-y-3">
                  <ToggleGroup type="single" value={timeMode} onValueChange={(v) => { if (v === "single" || v === "range") setTimeMode(v); }}>
                    <ToggleGroupItem value="single" size="sm">Singola</ToggleGroupItem>
                    <ToggleGroupItem value="range" size="sm">Intervallo</ToggleGroupItem>
                  </ToggleGroup>

                  {timeMode === "single" ? (
                    <div className="flex items-center gap-2">
                      <Input
                        type="time"
                        value={selectedTime}
                        onChange={(e) => {
                          const t = e.target.value;
                          setSelectedTime(t);
                          onTimeChange?.({ mode: "single", time: t });
                          setIsTimeOpen(false);
                        }}
                      />
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">Da</label>
                        <Input
                          type="time"
                          value={timeRange.start || ""}
                          onChange={(e) => {
                            const start = e.target.value;
                            setTimeRange((prev) => ({ ...prev, start }));
                          }}
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">A</label>
                        <Input
                          type="time"
                          value={timeRange.end || ""}
                          onChange={(e) => {
                            const end = e.target.value;
                            setTimeRange((prev) => ({ ...prev, end }));
                          }}
                        />
                      </div>
                      <div className="col-span-2 flex justify-end gap-2 pt-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setTimeRange({});
                          }}
                        >
                          Reset
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            if (timeRange.start && timeRange.end) {
                              onTimeChange?.({
                                mode: "range",
                                startTime: timeRange.start,
                                endTime: timeRange.end,
                              });
                              setIsTimeOpen(false);
                            }
                          }}
                          disabled={!(timeRange.start && timeRange.end)}
                        >
                          Applica
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </>
        )}
      </div>
    </div>
  );
};