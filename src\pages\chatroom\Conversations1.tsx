import { useNavigate, useSearchParams } from "react-router-dom";
import { useConversations } from "@/hooks/messages/useConversations";
import { useGetAllNotifications } from "@/hooks/notifications/useNotifications";
import { useSplitNotificationByType } from "@/hooks/notifications/useSplitNotificationByType";
import { format } from "date-fns";
import { it } from "date-fns/locale";

import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { EmptyConversationsState, EmptyNotificationsState } from "@/components/ui/empty-states";
import SwipeableGroupInviteCard from "@/components/notifications/SwipeableGroupInviteCard";
import EnhancedNotificationCard from "@/components/notifications/EnhancedNotificationCard";
import SwipeHintOverlay from "@/components/notifications/SwipeHintOverlay";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";

const Conversations = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { conversations, isLoading } = useConversations();
  const { data: notifications = [], isLoading: isLoadingNotifications } = useGetAllNotifications();
  const { isBusinessMode } = useBusinessMode();
  
  // Determina il tab attivo dai parametri URL o default a "conversations"
  const tabFromUrl = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState<"conversations" | "notifications">(
    tabFromUrl === 'notifications' ? 'notifications' : 'conversations'
  );

  // Gestione del tutorial swipe
  const [showSwipeHint, setShowSwipeHint] = useState(false);

  // Separiamo le conversazioni normali da quelle di booking
  const regularConversations = conversations.filter(conv => !conv.booking_id);
  const bookingConversations = conversations.filter(conv => conv.booking_id);

  // Usa il hook di deduplicazione
  const { 
    groupInviteNotifications, 
    bookingNotifications, 
    otherNotifications ,
    totalNotificationCount: totalCount
  } = useSplitNotificationByType(notifications);

  // Mostra il tutorial al primo accesso se ci sono inviti di gruppo
  useEffect(() => {
    const hasSeenSwipeHint = localStorage.getItem('hasSeenSwipeHint');
    if (!hasSeenSwipeHint && groupInviteNotifications.length > 0 && activeTab === 'notifications') {
      setShowSwipeHint(true);
    }
  }, [groupInviteNotifications.length, activeTab]);

  const handleDismissSwipeHint = () => {
    setShowSwipeHint(false);
    localStorage.setItem('hasSeenSwipeHint', 'true');
  };

  if (isLoading || isLoadingNotifications) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  const handleNotificationClick = (notification: any) => {
    // Gestisci il click sulla notifica basandosi sull'entity
    switch (notification.entity) {
      case 'group_invites':
        navigate('/groups');
        break;
      case 'bookings':
        // Naviga alla conversazione della prenotazione
        navigate(`/conversation/${notification.entity_id}`);
        break;
      default:
        console.log('Notifica cliccata:', notification);
    }
  };

  const handleGroupInviteProcessed = () => {
    // Forza il refresh delle notifiche dopo l'elaborazione
    window.location.reload();
  };

  // Message categories with icons and colors
  const messageCategories = [
    {
      id: 'promotional',
      title: 'Promozionale',
      description: 'Offerte esclusive, più economiche e veloci>>',
      icon: '🏷️',
      iconBg: 'bg-gradient-to-br from-purple-500 to-pink-500',
      count: notifications.filter(n => n.entity === 'deals').length,
      date: '23/07/2025'
    },
    {
      id: 'booking',
      title: 'Prenotazioni',
      description: bookingConversations.length > 0 ? `${bookingConversations.length} conversazioni attive` : 'Nessuna prenotazione',
      icon: '🚚',
      iconBg: 'bg-gradient-to-br from-teal-400 to-cyan-500',
      count: bookingConversations.length,
      date: ''
    },
    {
      id: 'system',
      title: 'Messaggi di Sistema',
      description: otherNotifications.length > 0 ? `${otherNotifications.length} messaggi` : 'Nessuna novità',
      icon: '🔔',
      iconBg: 'bg-gradient-to-br from-orange-400 to-red-500',
      count: otherNotifications.length,
      date: ''
    },
    {
      id: 'communities',
      title: 'Gruppi',
      description: groupInviteNotifications.length > 0 ? `${groupInviteNotifications.length} inviti` : 'Nessuna novità',
      icon: '💬',
      iconBg: 'bg-gradient-to-br from-blue-400 to-indigo-500',
      count: groupInviteNotifications.length,
      date: ''
    },
    {
      id: 'conversations',
      title: 'Conversazioni',
      description: regularConversations.length > 0 ? `${regularConversations.length} conversazioni` : 'Nessuna novità',
      icon: '📝',
      iconBg: 'bg-gradient-to-br from-red-400 to-pink-500',
      count: regularConversations.length,
      date: ''
    }
  ];

  const handleCategoryClick = (categoryId: string) => {
    switch(categoryId) {
      case 'promotional':
        setActiveTab('notifications');
        break;
      case 'booking':
        // Navigate to booking conversations
        if (bookingConversations.length > 0) {
          navigate(`/conversation/${bookingConversations[0].id}`);
        }
        break;
      case 'system':
        setActiveTab('notifications');
        break;
      case 'communities':
        navigate('/groups');
        break;
      case 'conversations':
        setActiveTab('conversations');
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="fixed top-0 left-0 right-0">
        <UnifiedHeader title="I Miei Messaggi" isBusiness={isBusinessMode} />
      </div>

      <main className="flex-1 overflow-y-auto pt-16 pb-20">
        {/* Message Categories */}
        <div className="px-4 py-6 space-y-3">
          {messageCategories.map((category) => (
            <div
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center gap-4">
                <div className={`w-12 h-12 rounded-full ${category.iconBg} flex items-center justify-center text-white text-xl relative`}>
                  <span>{category.icon}</span>
                  {category.count > 0 && (
                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
                      {category.count > 99 ? '99+' : category.count}
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-900 text-base">
                        {category.title}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {category.description}
                      </p>
                    </div>
                    {category.date && (
                      <span className="text-xs text-gray-400">
                        {category.date}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* New Message Section - Featured promotional content */}
        {notifications.filter(n => n.entity === 'deals').length > 0 && (
          <div className="px-4 pb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Nuovo messaggio</h3>
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <span className="text-white text-sm">🏷️</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Promozionale</h4>
                  <span className="text-xs text-gray-400">23/07/2025</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-orange-400 via-red-400 to-pink-400 rounded-lg p-4 text-center">
                <div className="text-black font-bold text-xl mb-2">Local Warehouse</div>
                <div className="text-black text-lg font-semibold">Spedizione Più Veloce</div>
                <div className="absolute bottom-2 left-4 text-black text-2xl font-bold">$</div>
              </div>
            </div>
          </div>
        )}

        {/* Tab Content for detailed views when needed */}
        {activeTab === "conversations" && (
          <div className="px-4 space-y-4">
            {regularConversations.length === 0 ? (
              <EmptyConversationsState />
            ) : (
              regularConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => navigate(`/conversation/${conversation.id}`)}
                  className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:bg-gray-50"
                >
                  <div className="flex items-center gap-4">
                    {conversation.business_photo ? (
                      <img
                        src={conversation.business_photo}
                        alt={conversation.business_name}
                        className="h-12 w-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-full bg-gray-200" />
                    )}
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium">
                            {conversation.business_name}
                          </h3>
                          {conversation.deal_title && (
                            <p className="text-sm text-gray-500">
                              {conversation.deal_title}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          {conversation.unread_count > 0 && (
                            <div className="bg-brand-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {conversation.unread_count}
                            </div>
                          )}
                          {conversation.last_message && (
                            <span className="text-xs text-gray-500">
                              {format(
                                new Date(conversation.last_message.created_at),
                                "HH:mm",
                                { locale: it }
                              )}
                            </span>
                          )}
                        </div>
                      </div>
                      {conversation.last_message && (
                        <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                          {conversation.last_message.content}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {activeTab === "notifications" && (
          <div className="px-4 space-y-4">
            {(groupInviteNotifications.length === 0 && bookingNotifications.length === 0 && otherNotifications.length === 0) ? (
              <EmptyNotificationsState />
            ) : (
              <>
                {groupInviteNotifications.map((notification) => (
                  <SwipeableGroupInviteCard
                    key={notification.id}
                    notification={notification}
                    onProcessed={handleGroupInviteProcessed}
                  />
                ))}
                {bookingNotifications.map((notification) => (
                  <EnhancedNotificationCard
                    key={notification.id}
                    notification={{
                      id: notification.id,
                      entity: notification.entity as 'booking' | 'messages' | 'group_invite' | 'others',
                      entity_id: notification.entity_id,
                      created_at: notification.created_at
                    }}
                    onClick={() => handleNotificationClick(notification)}
                    variant="default"
                  />
                ))}
                {otherNotifications.map((notification) => (
                  <EnhancedNotificationCard
                    key={notification.id}
                    notification={{
                      id: notification.id,
                      entity: notification.entity as 'booking' | 'messages' | 'group_invite' | 'others',
                      entity_id: notification.entity_id,
                      created_at: notification.created_at
                    }}
                    onClick={() => handleNotificationClick(notification)}
                    variant="default"
                  />
                ))}
              </>
            )}
          </div>
        )}
      </main>

      <div className="fixed bottom-0 left-0 right-0">
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>

      {/* Swipe Tutorial Overlay */}
      <SwipeHintOverlay 
        show={showSwipeHint}
        onDismiss={handleDismissSwipeHint}
      />
    </div>
  );
};

export default Conversations;
