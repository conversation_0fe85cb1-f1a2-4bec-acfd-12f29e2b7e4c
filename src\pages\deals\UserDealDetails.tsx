import { useNavigate, useParams } from "react-router-dom";
import type { WeeklySchedule } from "@/types/deals";
import { useDealDetails } from "@/hooks/deal/useDealDetails";
import { useRecordInteraction } from "@/hooks/useRecordInteraction";
import { useEffect, useState } from "react";

import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import DealDetailsImage from "@/components/deals/details/DealDetailsImage";
import DealDateSelector from "@/components/deals/booking/DealDateSelector";

import DealAvailability from "@/components/deals/booking/DealAvailability";
import DealMainInfo from "@/components/deals/core/DealMainInfo";
import DealBookingFooter from "@/components/deals/booking/DealBookingFooter";
import DealTimeSelector from "@/components/deals/booking/DealTimeSelector";
import { motion, AnimatePresence } from "framer-motion";
import {
  MicIcon,
  MessageCircleIcon,
  RefreshCwIcon,
  AlertCircleIcon,
  BotMessageSquareIcon,
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { createOrGetBusinessDealChatConversation } from "@/services/chatService";
import { useAuth } from "@/hooks/auth/useAuth";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import VoiceDialog from "@/features/voiceai/ui/VoiceDialog";
import { ShareButton } from "@/components/sharing/ShareButton";
import { ShareDealDialog } from "@/components/sharing/ShareDealDialog";

// Loading skeleton component
const DealDetailsskeleton = () => (
  <div className="min-h-screen bg-gray-50">
    <div className="pt-16 pb-20 px-4 space-y-6">
      {/* Image skeleton */}
      <div className="relative">
        <Skeleton className="w-full h-64 rounded-lg" />
        <div className="absolute top-4 right-4">
          <Skeleton className="w-16 h-8 rounded-full" />
        </div>
      </div>

      {/* Title and description skeleton */}
      <div className="space-y-3">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>

      {/* Availability section skeleton */}
      <Card>
        <CardContent className="p-6 space-y-4">
          <Skeleton className="h-6 w-1/3" />
          <div className="grid grid-cols-7 gap-2">
            {Array.from({ length: 7 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Footer skeleton */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-24" />
          </div>
          <Skeleton className="h-12 w-32" />
        </div>
      </div>
    </div>
  </div>
);

// Error component
const DealDetailsError = ({ onRetry }: { onRetry: () => void }) => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-md w-full"
    >
      <Alert className="mb-6">
        <AlertCircleIcon className="h-4 w-4" />
        <AlertDescription>
          Si è verificato un errore nel caricamento dell'offerta. Verifica la
          tua connessione e riprova.
        </AlertDescription>
      </Alert>

      <div className="flex flex-col items-center space-y-4">
        <Button onClick={onRetry} className="w-full" size="lg">
          <RefreshCwIcon className="mr-2 h-4 w-4" />
          Riprova
        </Button>
        <Button
          variant="outline"
          onClick={() => window.history.back()}
          className="w-full"
        >
          Torna indietro
        </Button>
      </div>
    </motion.div>
  </div>
);

const UserDealDetails = () => {
  const { id } = useParams();
  const { recordInteraction } = useRecordInteraction();
  const [isMessageLoading, setIsMessageLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpenVoiceDialog, setIsOpenVoiceDialog] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const {
    deal,
    selectedDate,
    setSelectedDate,
    selectedTime,
    setSelectedTime,
    isFavorite,
    availableDates,
    toggleFavorite,
  } = useDealDetails(id);

  const { user } = useAuth();
  const navigate = useNavigate();

  // Manage loading state based on deal data
  useEffect(() => {
    if (deal !== null) {
      setIsLoading(false);
      setError(null);
    } else if (id) {
      // If we have an ID but no deal after some time, consider it an error
      const timer = setTimeout(() => {
        if (deal === null) {
          setIsLoading(false);
          setError("Offerta non trovata");
        }
      }, 5000); // 5 second timeout

      return () => clearTimeout(timer);
    }
  }, [deal, id]);

  // Record interaction when deal loads
  useEffect(() => {
    if (id && deal) {
      recordInteraction(id, "view");
    }
  }, [id, deal, recordInteraction]);

  // Handle date selection and reset time when date changes
  const handleDateSelect = (date: string) => {
    setSelectedTime(null);
    setSelectedDate(date);
  };

  // Handle favorite toggle with optimistic UI
  const handleToggleFavorite = async () => {
    try {
      await toggleFavorite();
      if (id) {
        recordInteraction(id, "favorite");
      }
      toast.success(
        isFavorite ? "Rimosso dai preferiti" : "Aggiunto ai preferiti"
      );
    } catch (error) {
      toast.error("Errore nell'aggiornamento dei preferiti");
    }
  };

  // Handle retry
  const handleRetry = () => {
    setIsLoading(true);
    setError(null);
    // The useDealDetails hook will automatically re-fetch when component re-mounts
    window.location.reload();
  };

  // Handle message with voice
  const handleMessageWithVoice = async () => {

    //setIsOpenVoiceDialog(true);
    handleMessage();
  }
  // Handle message with loading state
  const handleMessage = async () => {
    if (!user) {
      toast.error("Devi essere loggato per contattare il gestore");
      return;
    }

    setIsMessageLoading(true);

    try {
      const { data: businessName, error: businessError } = await supabase
        .from("businesses")
        .select("name")
        .eq("id", deal!.business_id)
        .single();

      if (businessError) {
        throw new Error("Errore nel recupero del nome del business");
      }

      const result = await createOrGetBusinessDealChatConversation({
        businessId: deal!.business_id,
        dealId: deal!.id,
        userId: user.id,
        welcomeMessageData: {
          businessName: businessName?.name || "",
        },
      });

      toast.success("Conversazione creata con successo!");
      navigate(`/conversation/${result.conversationId}`);
    } catch (error) {
      console.error("Error creating conversation:", error);
      toast.error(
        "Si è verificato un errore nella creazione della conversazione"
      );
    } finally {
      setIsMessageLoading(false);
    }
  };

  // Loading state
  if (isLoading) {
    return <DealDetailsskeleton />;
  }

  // Error state
  if (error || !deal) {
    return <DealDetailsError onRetry={handleRetry} />;
  }

  const timeSlots: WeeklySchedule = deal.time_slots
    ? {
        schedule: (deal.time_slots as any).schedule || [],
        exceptions: (deal.time_slots as any).exceptions || [],
      }
    : { schedule: [], exceptions: [] };

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader
        variant="deal-details"
        showBackButton={true}
        isFavorite={isFavorite}
        onToggleFavorite={handleToggleFavorite}
        onShare={() => setIsShareDialogOpen(true)}
      />

      <main className="pt-16 pb-20">
        <AnimatePresence mode="wait">
          <motion.div
            key="deal-content"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6"
          >
            {/* Deal Image with enhanced visual hierarchy */}
            <div className="px-4">
              <DealDetailsImage
                images={deal.images}
                id={deal.id}
                title={deal.title}
                discountPercentage={deal.discount_percentage}
              />
            </div>

            {/* Main Info Section */}
            <div className="px-4">
              <DealMainInfo
                title={deal.title}
                description={deal.description || ""}
              />
            </div>

            {/* Contact Actions - Improved placement and UX */}
            <div className="px-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        Hai domande?
                      </h3>
                      <p className="text-sm text-gray-600">
                        Contatta il gestore per informazioni
                      </p>
                    </div>
                    <div className="flex">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={handleMessageWithVoice} // TODO: handleMessage based on user preferences or profile (need to make a decision)
                        disabled={isMessageLoading}
                        className="relative text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden min-w-[44px] min-h-[44px] flex items-center justify-center"
                        style={{
                          background:
                            "linear-gradient(-45deg, #ec4899, #a855f7, #ec4899, #f97316)",
                          backgroundSize: "400% 400%",
                          animation: "gradientSlide 3s ease infinite",
                        }}
                        aria-label="Assistente vocale"
                      >
                        {isMessageLoading ? (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 1,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                          >
                            <RefreshCwIcon className="h-4 w-4" />
                          </motion.div>
                        ) : (
                          // <MicIcon className="h-4 w-4 relative z-10" />
                          <BotMessageSquareIcon className="h-4 w-4 relative z-10" />
                        )}
                      </motion.button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Availability Section - Enhanced visual hierarchy */}
            <Card className="mx-4">
              <CardContent className="p-6">
                <DealAvailability
                  timeSlots={timeSlots}
                  startDate={deal.start_date}
                  endDate={deal.end_date}
                />

                <div className="mt-6 space-y-6">
                  <DealDateSelector
                    availableDates={availableDates}
                    selectedDate={selectedDate}
                    onDateSelect={handleDateSelect}
                  />

                  <AnimatePresence>
                    {selectedDate && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <DealTimeSelector
                          timeSlots={timeSlots}
                          selectedDate={selectedDate}
                          selectedTime={selectedTime}
                          onTimeSelect={setSelectedTime}
                          visible={!!selectedDate}
                          dealId={id}
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </AnimatePresence>
      </main>
      
      <VoiceDialog isOpen={isOpenVoiceDialog} onClose={() => setIsOpenVoiceDialog(false)} renderMode="conversation" businessId={deal.business_id} />
      
      <ShareDealDialog
        open={isShareDialogOpen}
        onOpenChange={setIsShareDialogOpen}
        dealId={deal.id}
        dealTitle={deal.title}
      />
      
      {/* Enhanced Footer with better accessibility */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg">
        <DealBookingFooter
          originalPrice={deal.original_price}
          discountedPrice={deal.discounted_price}
          discount={deal.discount_percentage}
          selectedDate={selectedDate}
          selectedTime={selectedTime}
          endTime={selectedTime}
          selectedDay={0}
          dealId={deal.id}
          hasTimeSlots={timeSlots.schedule.length > 0}
          businessId={deal.business_id}
        />
      </div>
    </div>
  );
};

export default UserDealDetails;
