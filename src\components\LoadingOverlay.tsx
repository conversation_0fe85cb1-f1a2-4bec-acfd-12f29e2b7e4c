// LoadingOverlay.tsx
import React from "react";

type Props = {
  message?: string;
};

export default function LoadingOverlay({ message = "Caricamento..." }: Props) {
  return (
    <div
      className="fixed inset-0 z-50 grid place-items-center bg-black/40 backdrop-blur-sm"
      role="status"
      aria-live="polite"
      aria-busy="true"
    >
      <div className="flex items-center gap-4 rounded-2xl bg-white p-6 shadow-xl">
        {/* Wheel spinner */}
        <div
          className="h-10 w-10 animate-spin rounded-full border-4 border-gray-300 border-t-transparent"
          aria-hidden="true"
        />
        <span className="text-gray-700">{message}</span>
      </div>

      {/* Reduce motion support */}
      <style>{`
        @media (prefers-reduced-motion: reduce) {
          .animate-spin { animation: none; }
        }
      `}</style>
    </div>
  );
}
