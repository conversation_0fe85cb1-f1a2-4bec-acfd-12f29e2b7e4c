import { useState, useRef } from "react";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addWeeks, subWeeks, isToday, isSameDay } from "date-fns";
import { it } from "date-fns/locale";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import ReminderBottomSheet from "./ReminderBottomSheet";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface WeekViewProps {
  reminders: Reminder[];
  onReminderClick?: (reminder: Reminder) => void;
}

const WeekView = ({ reminders, onReminderClick }: WeekViewProps) => {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(currentWeek, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  // Group reminders by date
  const remindersByDate = reminders.reduce((acc, reminder) => {
    const reminderDate = format(new Date(reminder.reminder_time), 'yyyy-MM-dd');
    if (!acc[reminderDate]) {
      acc[reminderDate] = [];
    }
    acc[reminderDate].push(reminder);
    return acc;
  }, {} as Record<string, Reminder[]>);

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek(prev => 
      direction === 'prev' ? subWeeks(prev, 1) : addWeeks(prev, 1)
    );
  };

  const handleDateClick = (day: Date) => {
    setSelectedDate(day);
    setIsBottomSheetOpen(true);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      navigateWeek('next');
    }
    if (isRightSwipe) {
      navigateWeek('prev');
    }
  };

  const selectedDateReminders = selectedDate 
    ? remindersByDate[format(selectedDate, 'yyyy-MM-dd')] || []
    : [];

  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i;
    return `${hour.toString().padStart(2, '0')}:00`;
  });

  return (
    <>
      <div className="bg-white rounded-t-2xl shadow-lg">
        {/* Week Navigation */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-slate-100">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('prev')}
            className="h-10 w-10 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div className="text-center">
            <h2 className="text-lg font-semibold text-slate-900">
              {format(weekStart, 'd MMM', { locale: it })} - {format(weekEnd, 'd MMM yyyy', { locale: it })}
            </h2>
            <p className="text-sm text-slate-600">Settimana</p>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateWeek('next')}
            className="h-10 w-10 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Week View */}
        <div 
          className="overflow-hidden"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Days Header */}
          <div className="grid grid-cols-7 border-b border-slate-100">
            {weekDays.map((day) => {
              const dayReminders = remindersByDate[format(day, 'yyyy-MM-dd')] || [];
              const todayDate = isToday(day);
              
              return (
                <div
                  key={format(day, 'yyyy-MM-dd')}
                  className={cn(
                    "p-3 text-center cursor-pointer hover:bg-slate-50 transition-colors",
                    todayDate && "bg-blue-50"
                  )}
                  onClick={() => handleDateClick(day)}
                >
                  <div className={cn(
                    "text-xs font-medium text-slate-600 mb-1",
                    todayDate && "text-blue-600"
                  )}>
                    {format(day, 'EEE', { locale: it })}
                  </div>
                  <div className={cn(
                    "text-lg font-semibold rounded-full w-8 h-8 flex items-center justify-center mx-auto",
                    todayDate && "bg-blue-600 text-white",
                    !todayDate && dayReminders.length > 0 && "bg-blue-100 text-blue-800",
                    !todayDate && dayReminders.length === 0 && "text-slate-900"
                  )}>
                    {format(day, 'd')}
                  </div>
                  {dayReminders.length > 0 && (
                    <div className="mt-1">
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full mx-auto",
                        todayDate && "bg-white",
                        !todayDate && "bg-blue-500"
                      )} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Simplified Mobile Week View */}
          <div className="max-h-[65vh] overflow-y-auto">
            {/* Display as cards for mobile instead of complex grid */}
            <div className="p-4 space-y-4">
              {weekDays.map((day) => {
                const dayReminders = remindersByDate[format(day, 'yyyy-MM-dd')] || [];
                const todayDate = isToday(day);
                
                return (
                  <div
                    key={format(day, 'yyyy-MM-dd')}
                    className={cn(
                      "bg-white rounded-xl border p-4 cursor-pointer transition-all",
                      todayDate && "border-blue-300 bg-blue-50 shadow-md",
                      !todayDate && dayReminders.length > 0 && "border-slate-200 hover:shadow-sm",
                      !todayDate && dayReminders.length === 0 && "border-slate-100 opacity-60"
                    )}
                    onClick={() => handleDateClick(day)}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className={cn(
                          "font-semibold text-base",
                          todayDate && "text-blue-900",
                          !todayDate && "text-slate-900"
                        )}>
                          {format(day, 'EEEE d', { locale: it })}
                        </h3>
                        {todayDate && (
                          <p className="text-sm text-blue-600 font-medium">Oggi</p>
                        )}
                      </div>
                      {dayReminders.length > 0 && (
                        <Badge 
                          variant="secondary" 
                          className={cn(
                            "text-xs font-medium",
                            todayDate && "bg-blue-200 text-blue-800"
                          )}
                        >
                          {dayReminders.length} promemori{dayReminders.length !== 1 ? '' : 'o'}
                        </Badge>
                      )}
                    </div>
                    
                    {dayReminders.length === 0 ? (
                      <p className="text-sm text-slate-500">Nessun promemoria</p>
                    ) : (
                      <div className="space-y-2">
                        {dayReminders
                          .sort((a, b) => new Date(a.reminder_time).getTime() - new Date(b.reminder_time).getTime())
                          .slice(0, 3)
                          .map((reminder) => (
                            <div
                              key={reminder.id}
                              className={cn(
                                "flex items-center space-x-3 p-2 rounded-lg text-sm",
                                reminder.status === 'pending' && "bg-amber-50 border border-amber-200",
                                reminder.status === 'sent' && "bg-emerald-50 border border-emerald-200",
                                reminder.status === 'cancelled' && "bg-slate-50 border border-slate-200",
                                reminder.status === 'failed' && "bg-red-50 border border-red-200"
                              )}
                            >
                              <div className={cn(
                                "w-2 h-2 rounded-full flex-shrink-0",
                                reminder.status === 'pending' && "bg-amber-500",
                                reminder.status === 'sent' && "bg-emerald-500",
                                reminder.status === 'cancelled' && "bg-slate-400",
                                reminder.status === 'failed' && "bg-red-500"
                              )} />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2">
                                  <span className="font-medium text-slate-900 truncate">
                                    {reminder.title}
                                  </span>
                                  <span className="text-xs text-slate-600 bg-white px-2 py-0.5 rounded-full font-mono">
                                    {format(new Date(reminder.reminder_time), 'HH:mm')}
                                  </span>
                                </div>
                              </div>
                            </div>
                          ))}
                        {dayReminders.length > 3 && (
                          <p className="text-xs text-slate-500 text-center pt-1">
                            +{dayReminders.length - 3} altri promemoria
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Swipe hint */}
          <div className="text-center py-4 border-t border-slate-100">
            <p className="text-xs text-slate-500">
              Scorri per navigare • Tocca per i dettagli
            </p>
          </div>
        </div>
      </div>

      <ReminderBottomSheet 
        isOpen={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
        date={selectedDate}
        reminders={selectedDateReminders}
      />
    </>
  );
};

export default WeekView;