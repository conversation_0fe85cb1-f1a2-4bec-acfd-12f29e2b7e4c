
import { useMemo, useState } from "react";
import { useBooking } from "@/hooks/booking/useBooking";
import { Euro, Calendar, LineChart } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { subDays, parseISO, format } from "date-fns";

const DATE_RANGES = {
  "7d": { label: "Ultimi 7 giorni", days: 7 },
  "30d": { label: "Ultimi 30 giorni", days: 30 },
  "90d": { label: "Ultimi 3 mesi", days: 90 },
  "365d": { label: "Ultimo anno", days: 365 },
  "all": { label: "Tutto", days: 0 },
} as const;

type DateRange = keyof typeof DATE_RANGES;

const SavingsAnalytics = () => {
  const [selectedRange, setSelectedRange] = useState<DateRange>("all");
  const { bookings } = useBooking();

  const stats = useMemo(() => {
    // Ottiene la data corrente senza ora/minuti/secondi
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);
    
    // Calcola la data di inizio in base all'intervallo selezionato
    let startDate = new Date(0);
    if (selectedRange !== "all") {
      startDate = subDays(currentDate, DATE_RANGES[selectedRange].days);
    }
    
    console.log(`Periodo selezionato: da ${startDate.toDateString()} a ${currentDate.toDateString()}`);

    // Filtra le prenotazioni in base alla data
    const filteredBookings = bookings.filter(booking => {
      if (!booking.booking_date) return false;
      
      try {
        // Converte la stringa della data di prenotazione in un oggetto Date
        const bookingDateStr = booking.booking_date; // formato "YYYY-MM-DD"
        const bookingDate = new Date(bookingDateStr);
        bookingDate.setHours(0, 0, 0, 0); // Imposta l'ora a mezzanotte
        
        // Verifica se la data della prenotazione è nell'intervallo selezionato
        const isInRange = selectedRange === "all" || 
          (bookingDate >= startDate && bookingDate <= currentDate);
        
        console.log(`Prenotazione ${booking.id}: data=${bookingDateStr}, inclusa=${isInRange}`);
        
        return isInRange;
      } catch (error) {
        console.error("Errore nel parsing della data:", booking.booking_date, error);
        return false;
      }
    });

    console.log("Date filtrate:", {
      selectedRange,
      startDate: startDate.toISOString(),
      endDate: currentDate.toISOString(),
      totalePrenotazioni: bookings.length,
      prenotazioniFiltrate: filteredBookings.length,
      prenotazioniIds: filteredBookings.map(b => b.id)
    });

    // Calcola il risparmio totale per tutte le prenotazioni filtrate
    const totalSaved = filteredBookings.reduce((acc, booking) => {
      const originalPrice = booking.original_price || 0;
      const discountedPrice = booking.discounted_price || 0;
      const saved = originalPrice - discountedPrice;
      return acc + saved;
    }, 0);

    const totalBookings = filteredBookings.length;
    const averageSaving = totalBookings > 0 ? totalSaved / totalBookings : 0;

    return {
      totalSaved,
      totalBookings,
      averageSaving
    };
  }, [bookings, selectedRange]);

  return (
    <div className="mt-6 mb-8">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <LineChart className="w-5 h-5 mr-2 text-brand-primary" />
          <h2 className="text-xl font-semibold text-gray-800">I tuoi risparmi</h2>
        </div>
        <Select 
          value={selectedRange} 
          onValueChange={(value: DateRange) => setSelectedRange(value)}
        >
          <SelectTrigger className="w-[160px] h-9 bg-white border-gray-200 shadow-sm text-sm">
            <Calendar className="w-4 h-4 mr-2 text-gray-500" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(DATE_RANGES).map(([key, { label }]) => (
              <SelectItem key={key} value={key}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <Card className="overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow">
          <div className="h-1.5 bg-gradient-to-r from-green-400 to-emerald-500"></div>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-14 h-14 rounded-full bg-green-50 mb-3">
                <Euro className="h-7 w-7 text-green-600" />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-1">
                €{stats.totalSaved.toFixed(2)}
              </h3>
              <p className="text-sm text-gray-600">
                Risparmiati in totale 
                <span className="ml-1 text-xs text-gray-500">
                  ({stats.totalBookings} prenotazioni)
                </span>
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="overflow-hidden border-0 shadow-md hover:shadow-lg transition-shadow">
          <div className="h-1.5 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-14 h-14 rounded-full bg-blue-50 mb-3">
                <Euro className="h-7 w-7 text-blue-600" />
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-1">
                €{stats.averageSaving.toFixed(2)}
              </h3>
              <p className="text-sm text-gray-600">Risparmio medio per prenotazione</p>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {stats.totalBookings === 0 && (
        <div className="mt-4 p-5 bg-gray-50 rounded-md text-center text-gray-600 border border-gray-100">
          <p className="mb-1 text-gray-700">Nessuna prenotazione trovata</p>
          <p className="text-sm">Non hai ancora prenotazioni nel periodo selezionato.</p>
        </div>
      )}
    </div>
  );
};

export default SavingsAnalytics;
