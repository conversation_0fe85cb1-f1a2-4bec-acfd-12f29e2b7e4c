import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";
import { corsHeaders } from "../_shared/cors.ts";
serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    // Get current date in ISO format
    const currentDate = new Date().toISOString();
    console.log(`Running expire-deals function at ${currentDate}`);
    
    // Update deals where end_date is in the past and status is not already 'expired'
    const { data, error, count } = await supabase.from("deals").update({
      status: "expired"
    }).lt("end_date", currentDate).neq("status", "expired").select();
        
    if (error) {
      console.error("Database update error:", error);
      return new Response(JSON.stringify({
        error: "Database update failed for expiring",
        details: error.message
      }), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 500
      });
    }
    console.log(`Successfully expired ${data.length} deals`);
const { data:dt, error:err, count:cnt } = await supabase
  .from("deals")
  .update({ status: "published" })
  .gte("end_date", currentDate)   // end_date today or in the future
  .eq("status", "expired")        // currently marked as expired
  .select();

    if (err) {
      console.error("Database update error:", err);
      return new Response(JSON.stringify({
        error: "Database update failed for not expiring",
        details: err.message
      }), {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json"
        },
        status: 500
      });
    }
    
    console.log(`Successfully republished ${dt.length} deals`);
    
    return new Response(JSON.stringify({
      success: true,
      message: `Successfully expired ${data.length} deals`,
      expired_deals: data
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      }
    });
  } catch (error) {
    console.error("Unexpected error:", error);
    return new Response(JSON.stringify({
      error: "Internal server error",
      details: error.message
    }), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json"
      },
      status: 500
    });
  }
});
