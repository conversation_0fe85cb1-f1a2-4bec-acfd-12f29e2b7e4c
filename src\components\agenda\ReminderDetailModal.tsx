import { format } from "date-fns";
import { it } from "date-fns/locale";
import { Calendar, Clock, Bell, AlertCircle, X } from "lucide-react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface ReminderDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  date: Date | null;
  reminders: Reminder[];
}

const ReminderDetailModal = ({ isOpen, onClose, date, reminders }: ReminderDetailModalProps) => {
  if (!date) return null;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-4 w-4" />;
      case 'task':
        return <Clock className="h-4 w-4" />;
      case 'booking':
        return <Bell className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500';
      case 'sent':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-gray-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'In attesa';
      case 'sent':
        return 'Inviato';
      case 'cancelled':
        return 'Annullato';
      case 'failed':
        return 'Fallito';
      default:
        return status;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-sm mx-auto">
        <DialogHeader>
          <DialogTitle className="text-lg">
            {format(date, 'EEEE d MMMM', { locale: it })}
          </DialogTitle>
        </DialogHeader>
        
        <div className="max-h-[60vh] overflow-y-auto">
          {reminders.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>Nessun promemoria per questo giorno</p>
            </div>
          ) : (
            <div className="space-y-3">
              {reminders
                .sort((a, b) => new Date(a.reminder_time).getTime() - new Date(b.reminder_time).getTime())
                .map((reminder) => (
                  <div
                    key={reminder.id}
                    className="p-3 bg-muted/50 rounded-lg space-y-2"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        {getTypeIcon(reminder.type)}
                        <h4 className="font-medium text-sm">{reminder.title}</h4>
                      </div>
                      <div
                        className={cn(
                          "w-2 h-2 rounded-full flex-shrink-0",
                          getStatusColor(reminder.status)
                        )}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-xs">
                        {format(new Date(reminder.reminder_time), 'HH:mm')}
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {getStatusText(reminder.status)}
                      </Badge>
                    </div>
                    
                    {reminder.description && (
                      <p className="text-xs text-muted-foreground">
                        {reminder.description}
                      </p>
                    )}
                  </div>
                ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReminderDetailModal;