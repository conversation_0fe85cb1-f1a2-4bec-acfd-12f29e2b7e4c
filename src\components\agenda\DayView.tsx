import { useState, useRef } from "react";
import { format, addDays, subDays, isToday } from "date-fns";
import { it } from "date-fns/locale";
import { ChevronLeft, ChevronRight, Calendar, Clock, Plus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface DayViewProps {
  reminders: Reminder[];
  onReminderClick?: (reminder: Reminder) => void;
  onAddReminder?: () => void;
}

const DayView = ({ reminders, onReminderClick, onAddReminder }: DayViewProps) => {
  const [currentDay, setCurrentDay] = useState(new Date());
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Filter reminders for the current day
  const dayReminders = reminders.filter(reminder => {
    const reminderDate = new Date(reminder.reminder_time);
    return format(reminderDate, 'yyyy-MM-dd') === format(currentDay, 'yyyy-MM-dd');
  }).sort((a, b) => new Date(a.reminder_time).getTime() - new Date(b.reminder_time).getTime());

  const navigateDay = (direction: 'prev' | 'next') => {
    setCurrentDay(prev => 
      direction === 'prev' ? subDays(prev, 1) : addDays(prev, 1)
    );
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      navigateDay('next');
    }
    if (isRightSwipe) {
      navigateDay('prev');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'sent':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'cancelled':
        return 'bg-slate-100 text-slate-600 border-slate-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-5 w-5" />;
      case 'task':
        return <Clock className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'In attesa';
      case 'sent':
        return 'Inviato';
      case 'cancelled':
        return 'Annullato';
      case 'failed':
        return 'Fallito';
      default:
        return status;
    }
  };

  const todayDate = isToday(currentDay);

  return (
    <>
      <div className="bg-white rounded-t-2xl shadow-lg">
        {/* Day Navigation */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-slate-100">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDay('prev')}
            className="h-10 w-10 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
          
          <div className="text-center max-w-[60%]">
            <h2 className={cn(
              "text-base font-semibold truncate",
              todayDate ? "text-blue-600" : "text-slate-900"
            )}>
              {format(currentDay, 'EEE d MMM', { locale: it })}
            </h2>
            {todayDate && (
              <p className="text-xs text-blue-600 font-medium">Oggi</p>
            )}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateDay('next')}
            className="h-10 w-10 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Day Summary */}
        <div className={cn(
          "px-6 py-4 border-b border-slate-100",
          todayDate && "bg-blue-50"
        )}>
          <div className="flex items-center justify-between">
            <div>
              <p className={cn(
                "text-sm font-medium",
                todayDate ? "text-blue-900" : "text-slate-700"
              )}>
                {dayReminders.length === 0 
                  ? "Nessun promemoria" 
                  : `${dayReminders.length} promemori${dayReminders.length !== 1 ? '' : 'o'}`
                }
              </p>
              {dayReminders.length > 0 && (
                <p className={cn(
                  "text-xs",
                  todayDate ? "text-blue-700" : "text-slate-500"
                )}>
                  {format(new Date(dayReminders[0].reminder_time), 'HH:mm')} - {format(new Date(dayReminders[dayReminders.length - 1].reminder_time), 'HH:mm')}
                </p>
              )}
            </div>
            <Button
              onClick={onAddReminder}
              size="sm"
              className={cn(
                "shadow-sm",
                todayDate ? "bg-blue-600 hover:bg-blue-700 text-white" : "bg-slate-900 hover:bg-slate-800 text-white"
              )}
            >
              <Plus className="h-4 w-4 mr-1" />
              Aggiungi
            </Button>
          </div>
        </div>

        {/* Day Content */}
        <div 
          className="max-h-[65vh] overflow-y-auto"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {dayReminders.length === 0 ? (
            <div className="text-center py-12 px-6">
              <div className="bg-slate-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                <Calendar className="h-10 w-10 text-slate-400" />
              </div>
              <p className="text-slate-600 mb-2">Giornata libera</p>
              <p className="text-slate-500 text-sm mb-4">
                {todayDate ? "Nessun promemoria per oggi" : "Nessun promemoria per questo giorno"}
              </p>
              <Button
                onClick={onAddReminder}
                variant="outline"
                className="text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Aggiungi promemoria
              </Button>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {dayReminders.map((reminder, index) => (
                <Card
                  key={reminder.id}
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => onReminderClick?.(reminder)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="bg-slate-100 rounded-lg p-2 flex-shrink-0">
                          {getTypeIcon(reminder.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-slate-900 truncate">
                            {reminder.title}
                          </h3>
                          {reminder.description && (
                            <p className="text-sm text-slate-600 mt-1 line-clamp-2">
                              {reminder.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between flex-wrap gap-2">
                      <div className="flex items-center space-x-2">
                        <div className="bg-white border border-slate-200 rounded-lg px-2 py-1.5 shadow-sm min-h-[40px] flex items-center">
                          <div className="flex items-center space-x-1.5">
                            <Clock className="h-4 w-4 text-slate-500" />
                            <span className="font-mono text-sm font-semibold">
                              {format(new Date(reminder.reminder_time), 'HH:mm')}
                            </span>
                          </div>
                        </div>
                        <Badge variant="outline" className={cn("border text-xs px-2 py-1", getStatusColor(reminder.status))}>
                          {getStatusText(reminder.status)}
                        </Badge>
                      </div>
                    </div>

                    {/* Additional info */}
                    <div className="flex items-center justify-between mt-3 pt-3 border-t border-slate-100">
                      <p className="text-xs text-slate-500">
                        Notifica: {reminder.notification_method}
                      </p>
                      {reminder.advance_minutes > 0 && (
                        <p className="text-xs text-slate-500">
                          {reminder.advance_minutes} min prima
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Swipe hint */}
          <div className="text-center py-4 border-t border-slate-100">
            <p className="text-xs text-slate-500">
              Scorri per navigare tra i giorni
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default DayView;