import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useNavigate, useSearchParams } from "react-router-dom";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import SearchPanel from "@/components/search/ModernSearchPanel";
import { useSearchPanel } from "@/hooks/useSearchPanel";
import { useFilterActions } from "@/stores/useFilterStore";
import { Map as MapIcon, List } from "lucide-react";
import { useAuth } from "@/hooks/auth/useAuth";
import { NearestBusinesses } from "@/components/map/NearestBusinesses";
import type { Database } from "@/integrations/supabase/types";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import DealCard from "@/components/deals/core/DealCard";
import { MapViewNew } from "@/components/map/MapViewNew";

import { useNearbyDealsSearch } from "@/hooks/search/useNearbyDealsSearch";
import type { DealAvailability } from "@/types/search";
import CoordinateFlowDebugger from "@/components/debug/CoordinateFlowDebugger";

import { LocationDateTimeBar } from "@/components/search/LocationDateTimeBar";
import { APIProvider } from "@vis.gl/react-google-maps";
import { useBusinessesWithDealsQuery } from "@/queries/useDealsQuery";
import { Button } from "@/components/ui/button";
import { UserPreferencesDebug } from "@/components/debug/UserPreferencesDebug";
// import { UserPreferencesDebug } from "@/components/debug/UserPreferencesDebug"; // For debugging

type Deal = Database["public"]["Tables"]["deals"]["Row"] & {
  businesses?: {
    name: string;
    address: string;
    city: string;
    zip_code: string;
    state: string;
    country: string;
    category_id: string;
    owner_id: string;
  };
};

type UnifiedDeal = Deal | DealAvailability;

const adaptDealForCard = (deal: UnifiedDeal): Deal => {
  if ("name" in deal) {
    // It's a DealAvailability, convert to Deal format
    return {
      id: deal.id,
      title: deal.name,
      description: deal.description,
      original_price: deal.original_price,
      discounted_price: deal.discounted_price,
      discount_percentage: deal.discount_percentage,
      images: deal.images,
      business_id: deal.business?.id || "",
      category_id: deal.business?.category_id || null,
      created_at: deal.created_at,
      updated_at: deal.updated_at,
      auto_confirm: deal.auto_confirm,
      bundle_discount_percentage: 0,
      is_bundle: false,
      start_date: deal.start_date,
      end_date: deal.end_date,
      terms_conditions: deal.terms_conditions || "",
      time_slots: deal.time_slots,
      status: "published" as any,
      fake: false,
      businesses: deal.business
        ? {
            name: deal.business.name,
            address: deal.business.address,
            city: deal.business.city,
            zip_code: deal.business.zip_code,
            state: deal.business.state,
            country: deal.business.country,
            category_id: deal.business.category_id,
            owner_id: "",
          }
        : undefined,
    };
  }
  // It's already a Deal
  return deal as Deal;
};

const Deals = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [urlSearchParams] = useSearchParams();
  const [apiKey, setApiKey] = useState<string>("");
  const [currentLocation, setCurrentLocation] = useState("Posizione Attuale");
  const [selectedLocationCoords, setSelectedLocationCoords] = useState<
    { lat: number; lng: number } | undefined
  >();
  const { isBusinessMode } = useBusinessMode();
  const [useFloatingSearch, setUseFloatingSearch] = useState(false);

  // Determine view mode based on URL parameters
  const hasListParam = urlSearchParams.has("list");
  const categoryParam = urlSearchParams.get("category");
  const [viewMode, setViewMode] = useState<"list" | "map">(
    hasListParam ? "list" : "map"
  );

  // Replace the manual useEffect with React Query
  const {
    data: businessesWithDealsData,
    isLoading,
    error: businessesError,
    isError,
  } = useBusinessesWithDealsQuery({
    enabled: !!user?.id,
  });

  // Extract businesses and deals from the query result
  const businesses = businessesWithDealsData?.businesses || [];
  const deals = businessesWithDealsData?.deals || [];

  // Nearby deals search hook
  const {
    results: nearbyDeals,
    isLoading: isLoadingNearby,
    updateSearchParams,
    params: nearbySearchParams,
  } = useNearbyDealsSearch();

  // Integrated SearchPanel - replaces old SearchBar + Categories + floating panel
  // Provides unified search, category filtering, business filters, location radius, and sorting
  const {
    isOpen: isSearchPanelOpen,
    filters,
    openSearchPanel,
    closeSearchPanel,
    updateFilters,
    hasActiveFilters,
    activeFiltersCount,
  } = useSearchPanel();

  // Import filter actions directly for URL parameter handling
  const filterActions = useFilterActions();
  const { setSelectedCategories } = filterActions;

  // Get Google Maps API key
  useEffect(() => {
    const getGoogleMapsKey = async () => {
      const {
        data: { GOOGLE_MAPS_API_KEY },
        error,
      } = await supabase.functions.invoke("get-google-maps-key");
      if (error) {
        console.error("Errore nel recupero della chiave API:", error);
        return;
      }
      setApiKey(GOOGLE_MAPS_API_KEY);
    };

    getGoogleMapsKey();
  }, []);

  // Handle React Query error
  useEffect(() => {
    if (isError && businessesError) {
      toast.error("Errore nel caricamento delle attività con offerte");
    }
  }, [isError, businessesError]);

  // Apply URL parameters on component mount
  useEffect(() => {
    // Apply category filter if present in URL
    if (categoryParam) {
      setSelectedCategories([categoryParam]);
    }
  }, [categoryParam, setSelectedCategories]);

  // Use nearby deals when floating search is active, otherwise use regular deals
  const displayedDeals: UnifiedDeal[] = useFloatingSearch ? nearbyDeals : deals;
  const isCurrentlyLoading = useFloatingSearch ? isLoadingNearby : isLoading;

  // Filter deals based on search panel filters
  const filteredDeals = displayedDeals.filter((deal) => {
    if (!deal) return false;

    // Handle different deal types
    const title = "name" in deal ? deal.name : deal.title;
    if (!title) return false;

    try {
      const dealTitle = title || "";
      const businessName =
        "business" in deal && deal.business
          ? deal.business.name
          : "businesses" in deal && deal.businesses
          ? deal.businesses.name
          : "";
      const businessAddress =
        "business" in deal && deal.business
          ? deal.business.address
          : "businesses" in deal && deal.businesses
          ? deal.businesses.address
          : "";
      const businessCategoryId =
        "business" in deal && deal.business
          ? deal.business.category_id
          : "businesses" in deal && deal.businesses
          ? deal.businesses.category_id
          : "";

      const matchesSearch =
        filters.searchQuery === "" ||
        dealTitle.toLowerCase().includes(filters.searchQuery.toLowerCase()) ||
        businessName
          .toLowerCase()
          .includes(filters.searchQuery.toLowerCase()) ||
        businessAddress
          .toLowerCase()
          .includes(filters.searchQuery.toLowerCase());

      const matchesCategory =
        !filters.selectedCategoryIds ||
        filters.selectedCategoryIds.length === 0 ||
        (businessCategoryId &&
          filters.selectedCategoryIds.includes(businessCategoryId));

      return matchesSearch && matchesCategory;
    } catch (error) {
      console.error("Error filtering deal:", error, deal);
      return false;
    }
  });

  // Handle floating search bar changes
  const handleLocationChange = (
    location: { lat: number; lng: number } | null,
    cityName?: string
  ) => {
    setUseFloatingSearch(true);
    updateSearchParams({
      location,
      cityName,
      date: nearbySearchParams.date,
      time: nearbySearchParams.time,
    });
  };

  const handleDateChange = (date: string) => {
    setUseFloatingSearch(true);
    updateSearchParams({
      date,
      location: nearbySearchParams.location,
      cityName: nearbySearchParams.cityName,
      time: nearbySearchParams.time,
    });
  };

  const handleTimeChange = (time: string) => {
    setUseFloatingSearch(true);
    updateSearchParams({
      time: time || undefined,
      date: nearbySearchParams.date,
      location: nearbySearchParams.location,
      cityName: nearbySearchParams.cityName,
    });
  };

  const handleGuestsChange = (guests: number) => {
    // Handle guests change if needed for future functionality
    console.log("Guests changed to:", guests);
  };

  // Handle location change from LocationDateTimeBar
  const handleLocationDateTimeBarChange = (
    location: string,
    place?: google.maps.places.Place
  ) => {
    setCurrentLocation(location);

    if (place && place.location) {
      const coordinates = place.location.toJSON();
      // Store the selected coordinates for the map
      setSelectedLocationCoords(coordinates);
      // Always use floating search when user manually selects location
      setUseFloatingSearch(true);
      updateSearchParams({
        location: coordinates,
        cityName: location,
        date: nearbySearchParams.date,
        time: nearbySearchParams.time,
      });
      console.log("Nuova posizione selezionata:", location, coordinates);
      console.log(
        "La nuova posizione sovrascrive quella precedente per tutte le ricerche"
      );
    } else {
      console.log("Posizione (solo testo):", location);
    }
  };

  // console.log("Final filteredDeals length:", filteredDeals.length);

  // Filter businesses for map view
  const filteredBusinesses = businesses.filter((business) => {
    try {
      const businessName = business.name || "";
      const businessCategoryId =
        business.categories?.id || business.category_id || "";

      const matchesSearch =
        filters.searchQuery === "" ||
        businessName.toLowerCase().includes(filters.searchQuery.toLowerCase());

      const matchesCategory =
        !filters.selectedCategoryIds ||
        filters.selectedCategoryIds.length === 0 ||
        (businessCategoryId &&
          filters.selectedCategoryIds.includes(businessCategoryId));

      return matchesSearch && matchesCategory;
    } catch (error) {
      console.error("Error filtering business:", error, business);
      return false;
    }
  });

  if (isCurrentlyLoading && !useFloatingSearch) {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <header className="fixed top-0 left-0 right-0 bg-white z-50 px-4 py-3 border-b">
          <h1 className="text-xl font-bold text-gray-800">Tutte le Offerte</h1>
        </header>
        <div className="p-4 pt-20">
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3, 4].map((item) => (
              <div
                key={item}
                className="bg-gray-100 rounded-xl h-48 animate-pulse"
              />
            ))}
          </div>
        </div>
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    );
  }

  return apiKey ? (
    <APIProvider apiKey={apiKey} libraries={["places"]}>
      <div className="min-h-screen bg-gray-50">
        <UnifiedHeader
          variant="dashboard"
          title="Tutte le Offerte"
          showFilters={true}
          onFilterClick={openSearchPanel}
          hasActiveFilters={hasActiveFilters}
          activeFiltersCount={activeFiltersCount}
        />

        {/* Location, Date, Time Toolbar */}
        <LocationDateTimeBar
          location={currentLocation}
          onLocationChange={handleLocationDateTimeBarChange}
        />

        {/* Search Panel */}
        <SearchPanel
          isOpen={isSearchPanelOpen}
          onClose={closeSearchPanel}
          filters={filters}
          onFiltersChange={updateFilters}
          placeholder="Cerca offerte e attività"
          showLocationFilter={true}
          showBusinessFilters={isBusinessMode}
          showTextSearch={false}
          showSortOptions={false}
          onDistanceChange={(distance) => {
            console.log("Distance changed to:", distance);
          }}
        />

        {/* View Mode Toggle Button */}
        <Button
          variant="ghost"
          onClick={() =>
            setViewMode((prev) => (prev === "list" ? "map" : "list"))
          }
          className="fixed right-4 top-36 z-50 bg-brand-primary shadow-lg rounded-full p-3 hover:bg-brand-primary/90 transition-colors"
        >
          {viewMode === "list" ? (
            <MapIcon className="h-5 w-5 text-white" />
          ) : (
            <List className="h-5 w-5 text-white" />
          )}
        </Button>

        {/* Content Area */}
        <main className="fixed inset-0 pt-12 pb-16">
          {viewMode === "list" ? (
            <div className="h-full overflow-y-auto px-4 py-4">
              <NearestBusinesses />

              {/* Debug: User Preferences Setup - Uncomment for debugging */}
              {/* <div className="mt-4 mb-6">
              <UserPreferencesDebug />
            </div> */}

              <div className="mt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">
                    Offerte Trovate ({filteredDeals.length})
                  </h2>
                  {filters.searchQuery && (
                    <p className="text-sm text-gray-600">
                      Risultati per: "{filters.searchQuery}"
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 gap-4 pb-4">
                  {filteredDeals.map((deal) => (
                    <DealCard
                      key={deal.id}
                      deal={adaptDealForCard(deal)}
                      variant="full"
                      onClick={() => navigate(`/deal/${deal.id}`)}
                      showVisitBusiness={true}
                    />
                  ))}
                  {filteredDeals.length === 0 && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">Nessuna offerta trovata</p>
                      <p className="text-sm text-gray-400 mt-2">
                        Prova a modificare i filtri di ricerca
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="h-full w-full relative">
              {/* Map fills the entire content area */}
              <MapViewNew
                businesses={filteredBusinesses}
                onDealClick={(dealId) => navigate(`/deal/${dealId}`)}
                userCircleRadius={filters.radius * 1000} // Convert km to meters
                selectedLocation={selectedLocationCoords}
                selectedLocationName={currentLocation}
                onLocationChange={setCurrentLocation}
              />

              {/* Coordinate Flow Debugger */}
              {/* <CoordinateFlowDebugger /> */}

              {/* Results count overlay for map view */}
              {filteredBusinesses.length > 0 && (
                <div className="absolute bottom-4 right-4 z-10 bg-white/30 shadow-lg rounded-full px-4 py-2 backdrop-blur-sm border border-slate-400">
                  <span className="text-sm font-medium text-gray-900">
                    {filteredBusinesses.length} attività trovate
                  </span>
                </div>
              )}
            </div>
          )}
        </main>

        {/* <CoordinateFlowDebugger /> */}
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>
    </APIProvider>
  ) : (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="animate-spin">
        <MapIcon className="h-8 w-8 text-brand-primary" />
      </div>
    </div>
  );
};

export default Deals;
