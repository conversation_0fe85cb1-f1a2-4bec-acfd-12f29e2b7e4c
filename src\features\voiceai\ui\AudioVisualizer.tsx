import { motion } from "framer-motion";

type AudioVisualizerProps = {
  audioLevel?: number;   // 0..1
  isOn?: boolean;        // default: true (ON)
};

export const AudioVisualizer: React.FC<AudioVisualizerProps> = ({
  audioLevel = 0.5,
  isOn = true,
}) => {
  // OFF: muted, static, slightly varied short bars to "feel off"
  if (!isOn) {
    return (
      <div
        className="flex items-center justify-center space-x-1 h-8 opacity-40"
        aria-label="Audio visualizer off"
      >
        {[...Array(7)].map((_, index) => (
          <div
            key={index}
            className="w-1 bg-white/30 rounded-full"
            style={{ height: 4 + ((index % 3) * 2) }} // subtle stagger
          />
        ))}
      </div>
    );
  }

  // ON: original rendering unchanged
  return (
    <div className="flex items-center justify-center space-x-1 h-8">
      {[...Array(7)].map((_, index) => (
        <motion.div
          key={index}
          className="w-1 bg-white/60 rounded-full"
          animate={{
            height: [
              8 + Math.sin((index + 1) * 0.5) * 4,
              8 + Math.sin((index + 1) * 0.5 + audioLevel * Math.PI) * 16,
              8 + Math.sin((index + 1) * 0.5) * 4,
            ],
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
};
