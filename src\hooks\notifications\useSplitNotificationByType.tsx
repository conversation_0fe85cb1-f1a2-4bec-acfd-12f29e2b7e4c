
import { useMemo } from 'react';


import { Notification } from '@/types/notifications';

export const useSplitNotificationByType = (
  notifications: Notification[]

) => {
  return useMemo(() => {
   
   


    // Separa le notifiche per tipo
    const groupInviteNotifications = notifications.filter(
      notification => notification.entity === 'group_invites'
    );
    
    const bookingNotifications = notifications.filter(
      notification => notification.entity === 'bookings'
    );
    
    const otherNotifications = notifications.filter(
      notification => !['group_invites', 'bookings'].includes(notification.entity)
    );

    return {
      groupInviteNotifications,
      bookingNotifications,
      otherNotifications,
      totalNotificationCount: groupInviteNotifications.length + bookingNotifications.length + otherNotifications.length
    };
  }, [notifications]);
};
