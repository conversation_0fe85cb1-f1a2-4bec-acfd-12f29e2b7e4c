import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { VolumeIcon } from "lucide-react";
import { useEffect, useRef, useState } from "react";

export interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
}
interface ConversationProps {
  messages: ChatMessage[];
  agentImageUrl: string;
  userImageUrl: string;
  userName: string;
}

const Conversation = ({
  messages,
  agentImageUrl,
  userImageUrl,
  userName,
}: ConversationProps) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showTopFade, setShowTopFade] = useState(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollTop } = scrollContainerRef.current;
      // Show fade when scrolled down more than 5px from the top
      setShowTopFade(scrollTop > 5);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  return (
    <div className="relative">
      {/* Top fade overlay - only visible when scrolled */}
      {showTopFade && (
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-b from-white/20 via-white/10 to-transparent z-10 pointer-events-none backdrop-blur-sm" />
      )}

      <div
        ref={scrollContainerRef}
        id="conversation-area"
        className="space-y-4 mb-6 h-96 overflow-y-auto"
      >
      {messages.map((message) => {
        const isUser = !!message.isUser;
        return (
          <div
            key={message.id}
            id={message.id}
            className={`flex items-start space-x-3 ${
              isUser ? "flex-row-reverse" : ""
            }`}
          >
            <div
              className={`w-8 h-8 ${
                isUser ? "bg-primary/10" : "bg-secondary/10"
              } rounded-full flex items-center justify-center flex-shrink-0`}
            >
              <Avatar className="w-6 h-6">
                <AvatarImage
                  src={isUser ? userImageUrl : agentImageUrl}
                  alt="English"
                />
                <AvatarFallback>
                  {isUser ? userName?.charAt(0) : "IA"}
                </AvatarFallback>
              </Avatar>
            </div>
            <div className="flex-1">
              <div
                className={`${
                  isUser
                    ? "bg-card text-card-foreground border border-border"
                    : "bg-primary text-primary-foreground"
                } rounded-2xl p-4 shadow-md`}
              >
                <p
                  className={`text-base text-left leading-relaxed ${
                    isUser ? "text-card-foreground" : "text-primary-foreground"
                  } font-medium`}
                >
                  {message.content}
                </p>
                <div className="flex items-center space-x-2">
                  <button
                    className={`inline-flex items-center justify-center rounded-md transition-colors
                      focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
                      focus-visible:ring-primary focus-visible:ring-offset-background
                      ${
                        isUser
                          ? "text-muted-foreground hover:text-foreground"
                          : "text-primary-foreground/90 hover:text-primary-foreground"
                      }`}
                    aria-label="Ascolta messaggio"
                  >
                    <VolumeIcon className="h-4 w-4" />
                    
                  </button>
                  <span
                    className={`text-xs ${
                      isUser
                        ? "text-muted-foreground"
                        : "text-primary-foreground/80"
                    }`}
                  >
                    {message.timestamp}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );
      })}
      <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default Conversation;
