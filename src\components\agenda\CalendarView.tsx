import { useState, useRef } from "react";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday, isSameDay } from "date-fns";
import { it } from "date-fns/locale";
import { ChevronLeft, ChevronRight, MoreHorizontal, Calendar, Clock, Bell, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import ReminderBottomSheet from "./ReminderBottomSheet";
import TodaySection from "./TodaySection";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface CalendarViewProps {
  reminders: Reminder[];
  onReminderClick?: (reminder: Reminder) => void;
  onAddReminder?: () => void;
}

const CalendarView = ({ reminders, onReminderClick, onAddReminder }: CalendarViewProps) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Group reminders by date
  const remindersByDate = reminders.reduce((acc, reminder) => {
    const reminderDate = format(new Date(reminder.reminder_time), 'yyyy-MM-dd');
    if (!acc[reminderDate]) {
      acc[reminderDate] = [];
    }
    acc[reminderDate].push(reminder);
    return acc;
  }, {} as Record<string, Reminder[]>);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-3 w-3" />;
      case 'task':
        return <Clock className="h-3 w-3" />;
      case 'booking':
        return <Bell className="h-3 w-3" />;
      default:
        return <AlertCircle className="h-3 w-3" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500';
      case 'sent':
        return 'bg-green-500';
      case 'cancelled':
        return 'bg-gray-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const handleDateClick = (day: Date) => {
    setSelectedDate(day);
    setIsBottomSheetOpen(true);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      navigateMonth('next');
    }
    if (isRightSwipe) {
      navigateMonth('prev');
    }
  };

  const selectedDateReminders = selectedDate 
    ? remindersByDate[format(selectedDate, 'yyyy-MM-dd')] || []
    : [];

  return (
    <>
      {/* Today Section */}
      <TodaySection 
        reminders={reminders}
        onReminderClick={onReminderClick || (() => {})}
        onAddReminder={onAddReminder || (() => {})}
      />

      <div className="bg-white rounded-t-2xl shadow-lg">
        {/* Month Navigation */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-slate-100">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('prev')}
            className="h-8 w-8 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <h2 className="text-base font-semibold text-slate-900 truncate max-w-[60%] text-center">
            {format(currentDate, 'MMM yyyy', { locale: it })}
          </h2>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigateMonth('next')}
            className="h-8 w-8 p-0 hover:bg-slate-100 rounded-full"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Calendar */}
        <div 
          className="px-2 pb-4"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          {/* Days of week */}
          <div className="grid grid-cols-7 gap-0.5 mb-1">
            {['L', 'M', 'M', 'G', 'V', 'S', 'D'].map((day, index) => (
              <div key={index} className="text-center text-xs font-semibold text-slate-600 py-1">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-0.5">
            {monthDays.map((day) => {
              const dayKey = format(day, 'yyyy-MM-dd');
              const dayReminders = remindersByDate[dayKey] || [];
              const isCurrentMonth = isSameMonth(day, currentDate);
              const hasReminders = dayReminders.length > 0;
              const todayDate = isToday(day);
              
              return (
                <Button
                  key={dayKey}
                  variant="ghost"
                  className={cn(
                    "h-16 w-full p-1 flex flex-col items-center justify-start relative rounded-lg transition-all duration-200 min-h-[44px] aspect-square",
                    !isCurrentMonth && "text-slate-400 opacity-40",
                    todayDate && "bg-blue-600 text-white hover:bg-blue-700 shadow-md",
                    !todayDate && hasReminders && "bg-blue-50 hover:bg-blue-100 border border-blue-200",
                    !todayDate && !hasReminders && "hover:bg-slate-50"
                  )}
                  onClick={() => handleDateClick(day)}
                >
                  <span className={cn(
                    "text-sm font-semibold mb-0.5",
                    todayDate && "text-white font-bold"
                  )}>
                    {format(day, 'd')}
                  </span>
                  
                  {/* Reminder indicators */}
                  {hasReminders && (
                    <div className="flex flex-col items-center space-y-0.5 w-full">
                      {/* Show dot indicator for reminders */}
                      <div className={cn(
                        "w-1.5 h-1.5 rounded-full",
                        todayDate && "bg-white",
                        !todayDate && "bg-blue-500"
                      )} />
                      
                      {/* Show count for multiple reminders */}
                      {dayReminders.length > 1 && (
                        <div className={cn(
                          "text-[10px] font-bold px-1 py-0.5 rounded-full leading-none",
                          todayDate && "bg-white/30 text-white",
                          !todayDate && "bg-blue-500 text-white"
                        )}>
                          {dayReminders.length}
                        </div>
                      )}
                    </div>
                  )}
                </Button>
              );
            })}
          </div>

          {/* Swipe hint */}
          <div className="text-center mt-4">
            <p className="text-xs text-slate-500">
              Scorri per navigare • Tocca una data per i dettagli
            </p>
          </div>
        </div>
      </div>

      <ReminderBottomSheet 
        isOpen={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
        date={selectedDate}
        reminders={selectedDateReminders}
      />
    </>
  );
};

export default CalendarView;