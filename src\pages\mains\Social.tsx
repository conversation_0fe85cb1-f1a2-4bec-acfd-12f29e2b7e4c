import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Users, MessageSquare, Sparkles, TrendingUp } from 'lucide-react';
import { SocialFeed } from '@/components/social/SocialFeed';
import { GroupsList } from '@/components/social/GroupsList';

export default function Social() {
  const [activeTab, setActiveTab] = useState('feed');

  return (
    <div className="flex flex-col">
      <div className="flex-1 max-w-4xl mx-auto w-full">
        {/* Header Section */}
        <div className="mb-4 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl mb-4 shadow-lg">
            <Sparkles className="h-8 w-8 text-primary-foreground" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-3">
            Social
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Scopri le migliori offerte condivise dai tuoi amici e connettiti con la community
          </p>
          
          {/* Stats Cards */}
          <div className="grid grid-cols-2 gap-4 mt-6 max-w-md mx-auto">
            <Card className="bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-5 w-5 text-primary mx-auto mb-2" />
                <p className="text-lg font-semibold text-foreground">24</p>
                <p className="text-xs text-muted-foreground">Offerte oggi</p>
              </CardContent>
            </Card>
            <Card className="bg-gradient-to-br from-secondary/5 to-secondary/10 border-secondary/20">
              <CardContent className="p-4 text-center">
                <Users className="h-5 w-5 text-secondary mx-auto mb-2" />
                <p className="text-lg font-semibold text-foreground">12</p>
                <p className="text-xs text-muted-foreground">Amici attivi</p>
              </CardContent>
            </Card>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-muted/50 backdrop-blur-sm border border-border/50">
            <TabsTrigger 
              value="feed" 
              className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              <MessageSquare className="h-4 w-4" />
              Feed Attività
            </TabsTrigger>
            <TabsTrigger 
              value="groups" 
              className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              <Users className="h-4 w-4" />
              I miei gruppi
            </TabsTrigger>
          </TabsList>

          <TabsContent value="feed" className="mt-8">
            <SocialFeed />
          </TabsContent>

          <TabsContent value="groups" className="mt-8">
            <GroupsList />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}