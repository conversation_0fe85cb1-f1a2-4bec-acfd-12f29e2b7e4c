import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useBusinessDetailsQuery = (businessId: string | undefined) => {
  return useQuery({
    queryKey: ['business-details', businessId],
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID is required');
      }

      const { data, error } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', businessId)
        .single();

      if (error) {
        throw new Error(`Errore nel caricamento dei dettagli dell'attività: ${error.message}`);
      }

      return data;
    },
    enabled: !!businessId,
    staleTime: 1000 * 60 * 10, // 10 minutes - business details rarely change
    refetchOnWindowFocus: false,
  });
};

export const useBusinessDealsQuery = (businessId: string | undefined) => {
  return useQuery({
    queryKey: ['business-deals', businessId],
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID is required');
      }

      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          businesses (
            name,
            address
          )
        `)
        .eq('business_id', businessId)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(`Errore nel caricamento delle offerte: ${error.message}`);
      }

      return data || [];
    },
    enabled: !!businessId,
    staleTime: 1000 * 60 * 5, // 5 minutes - deals change moderately
    refetchOnWindowFocus: false,
  });
};
