import { Building2 } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import ReactMarkdown from "react-markdown";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { MessageWithRole } from "@/types/conversations";
import { QRCodeSVG } from "qrcode.react";
import { z } from "zod";
interface AudioMessageProps {
  content: string;
}

interface AssistantMessageProps {
  message: MessageWithRole;
  isLoggedUsermessage: boolean;
  AudioMessage: React.FC<AudioMessageProps>;
  isBase64Audio: (content: string) => boolean;
  business_name: string; // This is a workaround because we don't have the business name the proper way in the message object yet
}

const AssistantMessage: React.FC<AssistantMessageProps> = ({
  message,
  isLoggedUsermessage,
  AudioMessage,
  isBase64Audio,
  business_name
}) => {

const MetadataSchema = z.object({
  ui: z.string().optional(),
  "qr-data": z.object({
    date: z.string(),
    time: z.string(),
    status: z.string(),
    bookingId: z.string(),
    dealTitle: z.string(),
    businessName: z.string(),
  }).optional(),
});

const parsed = MetadataSchema.safeParse(message.metadata);

//console.log("parsed", message.metadata,parsed);

  return (
    <div className={`flex ${isLoggedUsermessage ? "justify-end" : "justify-start"}`}>
      <div
        className={`flex items-end gap-2 max-w-[80%] ${
          isLoggedUsermessage ? "flex-row-reverse" : "flex-row"
        }`}
      >
        {/* Avatar */}
        <Avatar className="h-8 w-8 bg-indigo-500 text-white">
          <AvatarFallback>
            <Building2 className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>

        {/* Message Content */}
        <div className="rounded-lg p-3 shadow-sm bg-white border rounded-tl-none">
          {/* Sender Name */}
          <div className="text-xs mb-1 font-medium text-gray-500">
            {isLoggedUsermessage ? "Tu" : business_name}
          </div>

          {/* Message Content or Audio */}
          {isBase64Audio(message.content) ? (
            <AudioMessage content={message.content} />
          ) : (
            <div className="text-sm prose prose-sm dark:prose-invert max-w-none">
              <ReactMarkdown>{message.content}</ReactMarkdown>
              {parsed.success && parsed.data.ui ===
                  "booking-data" && (
                    <div className="bg-white p-2 my-2 rounded-xl shadow-sm border">
                      <QRCodeSVG 
                        value={JSON.stringify(parsed.data["qr-data"])} 
                        size={200} 
                      />
                    </div>
                  )}

                  {/* {parsed.success && parsed.data.ui ===
                  "available_deals" && (
                    <div className="bg-white p-2 my-2 rounded-xl shadow-sm border">
                      <p>{parsed.data["deals"].map((deal: any) => (
                        <div key={deal.id}>
                          <p>{deal.name}</p>
                        </div>
                      ))}</p>
                    </div>
                  )} */}
            </div>
          )}

          {/* Timestamp */}
          <div className="text-xs mt-1 text-gray-500">
            {format(new Date(message.created_at), "HH:mm", {
              locale: it,
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssistantMessage;
