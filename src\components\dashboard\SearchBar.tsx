import { Plus, Mic } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

/**
 * SearchBar Component
 *
 * Ricerca a pill con icona e microfono integrato.
 */
interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onVoiceClick?: () => void;
}

const SearchBar = ({
  value,
  onChange,
  placeholder = "Cerca offerte e attività",
  onVoiceClick,
}: SearchBarProps) => {
  return (
    <div role="search" aria-label="Barra di ricerca offerte">
      <div className="flex items-center rounded-full border border-input bg-background/70 px-3 py-2 focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:ring-offset-background">
        <Plus className="mr-2 h-5 w-5 text-muted-foreground" aria-hidden="true" />
        <input
          type="text"
          placeholder={placeholder}
          className="flex-1 bg-transparent text-foreground placeholder:text-muted-foreground outline-none"
          value={value}
          onChange={(e) => onChange(e.target.value)}
        />
        {onVoiceClick && (
          <Button
            type="button"
            variant="secondary"
            size="icon"
            aria-label="Assistente vocale"
            className="ml-2 rounded-full"
            onClick={onVoiceClick}
          >
            <Mic className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default SearchBar;
