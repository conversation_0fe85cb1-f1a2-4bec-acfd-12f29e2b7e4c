import { useNavigate, useSearchParams } from "react-router-dom";
import { useConversations } from "@/hooks/messages/useConversations";
import { useGetAllNotifications } from "@/hooks/notifications/useNotifications";
import { useSplitNotificationByType } from "@/hooks/notifications/useSplitNotificationByType";
import { format } from "date-fns";
import { it } from "date-fns/locale";

import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";
import { EmptyConversationsState, EmptyNotificationsState } from "@/components/ui/empty-states";
import SwipeableGroupInviteCard from "@/components/notifications/SwipeableGroupInviteCard";
import EnhancedNotificationCard from "@/components/notifications/EnhancedNotificationCard";
import SwipeHintOverlay from "@/components/notifications/SwipeHintOverlay";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";

const Conversations = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { conversations, isLoading } = useConversations();
  
  const { data: notifications = [], isLoading: isLoadingNotifications } = useGetAllNotifications();
  
  const { isBusinessMode } = useBusinessMode();
  
  // Determina il tab attivo dai parametri URL o default a "conversations"
  const tabFromUrl = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState<"conversations" | "notifications">(
    tabFromUrl === 'notifications' ? 'notifications' : 'conversations'
  );

  // Gestione del tutorial swipe
  const [showSwipeHint, setShowSwipeHint] = useState(false);

  // Separiamo le conversazioni normali da quelle di booking
  //const regularConversations = conversations.filter(conv => !conv.booking_id);
  const regularConversations = conversations;
  const bookingConversations = conversations.filter(conv => conv.booking_id);

  // Usa il hook di deduplicazione // TODO Is it necessary?
  const { 
    groupInviteNotifications, 
    bookingNotifications, 
    otherNotifications ,
    totalNotificationCount
  } = useSplitNotificationByType(notifications);

  // Mostra il tutorial al primo accesso se ci sono inviti di gruppo
  useEffect(() => {
    const hasSeenSwipeHint = localStorage.getItem('hasSeenSwipeHint');
    if (!hasSeenSwipeHint && groupInviteNotifications.length > 0 && activeTab === 'notifications') {
      setShowSwipeHint(true);
    }
  }, [groupInviteNotifications.length, activeTab]);

  const handleDismissSwipeHint = () => {
    setShowSwipeHint(false);
    localStorage.setItem('hasSeenSwipeHint', 'true');
  };

  if (isLoading || isLoadingNotifications) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  const handleNotificationClick = (notification: any) => {
    // Gestisci il click sulla notifica basandosi sull'entity
    switch (notification.entity) {
      case 'group_invites':
        navigate('/groups');
        break;
      case 'bookings':
        // Naviga alla conversazione della prenotazione
        navigate(`/conversation/${notification.entity_id}`);
        break;
      default:
        console.log('Notifica cliccata:', notification);
    }
  };

  const handleGroupInviteProcessed = () => {
    // Forza il refresh delle notifiche dopo l'elaborazione
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col max-w-md mx-auto">
      <div className="fixed top-0 left-0 right-0 max-w-md mx-auto z-50">
        <UnifiedHeader title="Messaggi" isBusiness={isBusinessMode} />
      </div>

      <main className="flex-1 overflow-y-auto pt-16 pb-20 px-3 safe-area-inset-top safe-area-inset-bottom">
        {/* Tab Buttons - Mobile Optimized */}
        <div className="flex items-center justify-center gap-1 bg-muted/50 rounded-xl p-1 mb-4 mt-4 mx-1">
          <Button
            size="sm"
            className={`text-xs rounded-lg flex-1 transition-all duration-300 min-w-0 ${
              activeTab === "conversations"
                ? "bg-primary text-white shadow-sm"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground"
            }`}
            onClick={() => setActiveTab("conversations")}
          >
            <span className="truncate">Conversazioni</span>
          </Button>
          <Button
            size="sm"
            className={`text-xs rounded-lg flex-1 transition-all duration-300 min-w-0 relative ${
              activeTab === "notifications"
                ? "bg-primary text-white shadow-sm"
                : "bg-transparent text-muted-foreground hover:bg-background hover:text-foreground"
            }`}
            onClick={() => setActiveTab("notifications")}
          >
            <span className="truncate">Notifiche</span>
            {totalNotificationCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs rounded-full w-4 h-4 flex items-center justify-center text-[10px] font-medium">
                {totalNotificationCount > 9 ? "9+" : totalNotificationCount}
              </span>
            )}
          </Button>
        </div>

        {/* Content Container - Mobile Optimized */}
        <div className="relative">
          {activeTab === "conversations" ? (
            /* Conversations Tab Content */
            <div className="w-full">
              {regularConversations.length === 0 ? (
                <EmptyConversationsState />
              ) : (
                <div className="space-y-4">
                  {regularConversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      onClick={() => navigate(`/conversation/${conversation.id}`)}
                      className="bg-white rounded-lg p-4 shadow-sm cursor-pointer hover:bg-gray-50 relative"
                    >
                      <div className="flex items-center gap-4">
                        {conversation.business_photo ? (
                          <img
                            src={conversation.business_photo}
                            alt={conversation.business_name}
                            className="h-12 w-12 rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-12 w-12 rounded-full bg-gray-200" />
                        )}
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium">
                                {conversation.business_name}
                              </h3>
                              <div className="flex items-center gap-2">
                                {conversation.deal_title && (
                                  <p className="text-sm text-gray-500">
                                    {conversation.deal_title}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {conversation.unread_count > 0 && (
                                <div className="bg-brand-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                  {conversation.unread_count}
                                </div>
                              )}
                              {conversation.last_message && (
                                <span className="text-xs text-gray-500">
                                  {format(
                                    new Date(conversation.last_message.created_at),
                                    "HH:mm",
                                    {
                                      locale: it,
                                    }
                                  )}
                                </span>
                              )}
                            </div>
                          </div>
                          {conversation.last_message && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                              {conversation.last_message.content}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

          ) : (
            /* Notifications Tab Content */
            <div className="w-full">
              {(totalNotificationCount === 0) ? (
                <EmptyNotificationsState />
              ) : (
                <div className="space-y-4">
                  {/* Group Invite Cards - Swipeable */}
                  {groupInviteNotifications.map((notification) => (
                    <SwipeableGroupInviteCard
                      key={notification.id}
                      notification={notification}
                      onProcessed={handleGroupInviteProcessed}
                    />
                  ))}

                  {/* Booking Notifications - Stile distintivo */}
                  {/* {bookingConversations.map((conversation) => (
                    <EnhancedNotificationCard
                      key={`booking-conv-${conversation.id}`}
                      notification={{
                        id: conversation.id,
                        entity: 'bookings',
                        entity_id: conversation.id,
                        created_at: conversation.last_message?.created_at || new Date().toISOString()
                      }}
                      onClick={() => navigate(`/conversation/${conversation.id}`)}
                      variant="booking"
                    />
                  ))} */}

                  {/* Booking Notifications - Solo se non duplicate */}
                  {bookingNotifications.map((notification) => (
                    <EnhancedNotificationCard
                      key={`booking-${notification.id}`}
                       notification={{
                        id: notification.id,
                        entity: "booking",
                        entity_id: notification.id,
                        created_at: notification.created_at
                      }}
                      onClick={() => handleNotificationClick(notification)}
                      variant="default"
                    />
                  ))}

                  {/* Other Notifications */}
                  {otherNotifications.map((notification) => (
                    <EnhancedNotificationCard
                      key={notification.id}
                      notification={{
                        id: notification.id,
                        entity: notification.entity as 'booking' | 'messages' | 'group_invite' | 'others',
                        entity_id: notification.entity_id,
                        created_at: notification.created_at
                      }}
                      onClick={() => handleNotificationClick(notification)}
                      variant="default"
                    />
                   ))}
                 </div>
               )}
             </div>
           )}
         </div>
      </main>

      <div className="fixed bottom-0 left-0 right-0 max-w-md mx-auto">
        <BottomNavigationBar isBusiness={isBusinessMode} />
      </div>

      {/* Swipe Tutorial Overlay */}
      <SwipeHintOverlay 
        show={showSwipeHint}
        onDismiss={handleDismissSwipeHint}
      />
    </div>
  );
};

export default Conversations;
