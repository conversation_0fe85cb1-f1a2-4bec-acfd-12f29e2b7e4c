-- Remove thread_id from user_details table
ALTER TABLE public.user_details DROP COLUMN IF EXISTS thread_id;

-- Create conversations_thread table
CREATE TABLE public.conversations_thread (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  conversation_id UUID NOT NULL REFERENCES public.conversations(id) ON DELETE CASCADE,
  thread_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.conversations_thread ENABLE ROW LEVEL SECURITY;

-- Create policies for conversations_thread
CREATE POLICY "Users can view conversation threads they participate in" 
ON public.conversations_thread 
FOR SELECT 
USING (
  conversation_id IN (
    SELECT c.id 
    FROM conversations c
    JOIN conversation_participants cp ON c.id = cp.conversation_id
    WHERE cp.user_id = auth.uid()
  )
);

CREATE POLICY "Users can create conversation threads for conversations they participate in" 
ON public.conversations_thread 
FOR INSERT 
WITH CHECK (
  conversation_id IN (
    SELECT c.id 
    FROM conversations c
    JOIN conversation_participants cp ON c.id = cp.conversation_id
    WHERE cp.user_id = auth.uid()
  )
);

CREATE POLICY "Users can update conversation threads for conversations they participate in" 
ON public.conversations_thread 
FOR UPDATE 
USING (
  conversation_id IN (
    SELECT c.id 
    FROM conversations c
    JOIN conversation_participants cp ON c.id = cp.conversation_id
    WHERE cp.user_id = auth.uid()
  )
);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_conversations_thread_updated_at
BEFORE UPDATE ON public.conversations_thread
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();