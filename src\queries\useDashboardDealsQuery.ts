import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Deal } from '@/types/deals';

export const useRecentlyViewedDealsQuery = (userId: string | undefined) => {
  return useQuery({
    queryKey: ['recently-viewed-deals', userId],
    queryFn: async (): Promise<Deal[]> => {
      if (!userId) {
        return [];
      }

      const { data: userDetails, error: userDetailsError } = await supabase
        .from('user_details')
        .select('recently_viewed_deals')
        .eq('id', userId)
        .single();

      if (userDetailsError) {
        throw new Error(`Errore nel recupero dei dettagli utente: ${userDetailsError.message}`);
      }

      if (!userDetails?.recently_viewed_deals || (Array.isArray(userDetails.recently_viewed_deals) && userDetails.recently_viewed_deals.length === 0)) {
        return [];
      }

      const { data: dealsData, error: dealsError } = await supabase
        .from('deals')
        .select('*, businesses(*)')
        .in('id', userDetails.recently_viewed_deals as string[])
        .eq('status', 'published');

      if (dealsError) {
        throw new Error(`Errore nel recupero delle offerte viste di recente: ${dealsError.message}`);
      }

      return dealsData as Deal[] || [];
    },
    enabled: !!userId,
    staleTime: 1000 * 60 * 5, // 5 minutes - deals change moderately
    refetchOnWindowFocus: false,
  });
};

export const useFavoriteDealsQuery = (userId: string | undefined) => {
  return useQuery({
    queryKey: ['favorite-deals', userId],
    queryFn: async (): Promise<Deal[]> => {
      if (!userId) {
        return [];
      }

      const { data: userDetails, error: userDetailsError } = await supabase
        .from('user_details')
        .select('favorite_deals')
        .eq('id', userId)
        .single();

      if (userDetailsError) {
        throw new Error(`Errore nel recupero dei dettagli utente: ${userDetailsError.message}`);
      }

      if (!userDetails?.favorite_deals || (Array.isArray(userDetails.favorite_deals) && userDetails.favorite_deals.length === 0)) {
        return [];
      }

      const { data: dealsData, error: dealsError } = await supabase
        .from('deals')
        .select('*, businesses(*)')
        .in('id', userDetails.favorite_deals as string[])
        .eq('status', 'published');

      if (dealsError) {
        throw new Error(`Errore nel recupero delle offerte preferite: ${dealsError.message}`);
      }

      return dealsData as Deal[] || [];
    },
    enabled: !!userId,
    staleTime: 1000 * 60 * 5, // 5 minutes - deals change moderately
    refetchOnWindowFocus: false,
  });
};
