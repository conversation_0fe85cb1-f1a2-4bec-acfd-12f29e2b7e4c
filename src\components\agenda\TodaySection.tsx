import { format, isToday } from "date-fns";
import { it } from "date-fns/locale";
import { Calendar, Clock, Plus, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface TodaySectionProps {
  reminders: Reminder[];
  onReminderClick: (reminder: Reminder) => void;
  onAddReminder: () => void;
}

const TodaySection = ({ reminders, onReminderClick, onAddReminder }: TodaySectionProps) => {
  const today = new Date();
  const todayReminders = reminders.filter(r => isToday(new Date(r.reminder_time)));
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-500';
      case 'sent':
        return 'bg-emerald-500';
      case 'cancelled':
        return 'bg-slate-400';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-4 w-4" />;
      case 'task':
        return <Clock className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  if (todayReminders.length === 0) {
    return (
      <Card className="mx-4 mb-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h2 className="text-lg font-semibold text-slate-900">Oggi</h2>
              <p className="text-sm text-slate-600">
                {format(today, 'EEEE d MMMM', { locale: it })}
              </p>
            </div>
            <Button
              onClick={onAddReminder}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            >
              <Plus className="h-4 w-4 mr-1" />
              Aggiungi
            </Button>
          </div>
          <div className="text-center py-6">
            <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
              <Calendar className="h-8 w-8 text-blue-600" />
            </div>
            <p className="text-slate-600 text-sm">Nessun promemoria per oggi</p>
            <p className="text-slate-500 text-xs mt-1">Goditi la giornata libera! 🌟</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="mx-4 mb-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold text-slate-900">Oggi</h2>
            <p className="text-sm text-slate-600">
              {format(today, 'EEEE d MMMM', { locale: it })}
            </p>
          </div>
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {todayReminders.length} promemori{todayReminders.length !== 1 ? '' : 'o'}
          </Badge>
        </div>
        
        <div className="space-y-2">
          {todayReminders
            .sort((a, b) => new Date(a.reminder_time).getTime() - new Date(b.reminder_time).getTime())
            .slice(0, 3)
            .map((reminder) => (
              <div
                key={reminder.id}
                onClick={() => onReminderClick(reminder)}
                className="bg-white/80 backdrop-blur-sm rounded-lg p-3 cursor-pointer hover:bg-white/90 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="flex items-center justify-center">
                      <div className={cn("w-2 h-2 rounded-full mr-2", getStatusColor(reminder.status))} />
                      {getTypeIcon(reminder.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-slate-900 text-sm truncate">
                          {reminder.title}
                        </p>
                        <Badge variant="outline" className="text-xs bg-white/50">
                          {format(new Date(reminder.reminder_time), 'HH:mm')}
                        </Badge>
                      </div>
                      {reminder.description && (
                        <p className="text-xs text-slate-600 truncate mt-1">
                          {reminder.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <ChevronRight className="h-4 w-4 text-slate-400" />
                </div>
              </div>
            ))}
          
          {todayReminders.length > 3 && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50"
            >
              Vedi altri {todayReminders.length - 3} promemoria
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};

export default TodaySection;