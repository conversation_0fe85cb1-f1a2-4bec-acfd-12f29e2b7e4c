import { Database } from "@/integrations/supabase/types";

// Add Deal type
export type Deal = Database["public"]["Tables"]["deals"]["Row"] & {
  businesses?: {
    name: string;
    address: string;
    city: string;
    zip_code: string;
    state: string;
    country: string;
  };
};

// Add Booking type
export type Booking = Database["public"]["Tables"]["bookings"]["Row"] & {
  deals?: {
    title: string;
    images: string[] | null;
    discounted_price: number;
    businesses?: {
      name: string;
      address: string;
      city: string;
      zip_code: string;
      state: string;
      country: string;
    } | null;
  } | null;
};



