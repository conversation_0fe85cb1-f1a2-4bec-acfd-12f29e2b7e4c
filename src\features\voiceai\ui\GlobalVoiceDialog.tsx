import React from 'react';
import VoiceDialog from '@/features/voiceai/ui/VoiceDialog';
import { VoiceDialogRenderMode } from "../types/VoiceDialogRenderMode";
import { useVoiceDialog } from '@/contexts/VoiceDialogContext';



interface GlobalVoiceDialogProps {
  renderMode?: VoiceDialogRenderMode;
}

const GlobalVoiceDialog: React.FC<GlobalVoiceDialogProps> = ({
  renderMode = "conversation"
}) => {
  
  const { isVoiceDialogOpen, closeVoiceDialog, businessId } = useVoiceDialog();

  return (
    <VoiceDialog
      isOpen={isVoiceDialogOpen}
      onClose={closeVoiceDialog}
      renderMode={renderMode}
      businessId={businessId}
    />
  );
};

export default GlobalVoiceDialog;
