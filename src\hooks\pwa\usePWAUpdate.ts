import { useState, useEffect, useRef } from 'react';

interface PWAUpdateState {
  updateAvailable: boolean;
  isUpdating: boolean;
  updateAndReload: () => void;
  dismissUpdate: () => void;
}

export const usePWAUpdate = (): PWAUpdateState => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [waitingWorker, setWaitingWorker] = useState<ServiceWorker | null>(null);
  const updateTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      let registration: ServiceWorkerRegistration;
      
      const handleUpdateFound = () => {
        const newWorker = registration.installing;
        if (newWorker) {
          const handleStateChange = () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setWaitingWorker(newWorker);
              setUpdateAvailable(true);
              newWorker.removeEventListener('statechange', handleStateChange);
            }
          };
          newWorker.addEventListener('statechange', handleStateChange);
        }
      };

      navigator.serviceWorker.getRegistration().then((reg) => {
        if (reg) {
          registration = reg;
          
          if (reg.waiting) {
            setWaitingWorker(reg.waiting);
            setUpdateAvailable(true);
          }

          // Listen for updates
          reg.addEventListener('updatefound', handleUpdateFound);
        }
      });

      return () => {
        if (registration) {
          registration.removeEventListener('updatefound', handleUpdateFound);
        }
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }
      };
    }
  }, []);

  const updateAndReload = () => {
    if (waitingWorker && !isUpdating) {
      setIsUpdating(true);
      
      updateTimeoutRef.current = setTimeout(() => {
        console.warn('Update timeout - forcing reload');
        window.location.reload();
      }, 10000); // 10 second timeout

      const handleActivation = () => {
        if (updateTimeoutRef.current) {
          clearTimeout(updateTimeoutRef.current);
        }
        setIsUpdating(false);
        // Small delay before reload
        setTimeout(() => {
          window.location.reload();
        }, 500);
      };

      waitingWorker.addEventListener('statechange', () => {
        if (waitingWorker.state === 'activated') {
          handleActivation();
        }
      }, { once: true }); // Use 'once' to auto-remove listener

      waitingWorker.postMessage({ type: 'SKIP_WAITING' });
    }
  };

  const dismissUpdate = () => {
    setUpdateAvailable(false);
    setWaitingWorker(null);
    if (updateTimeoutRef.current) {
      clearTimeout(updateTimeoutRef.current);
    }
  };

  return {
    updateAvailable,
    isUpdating,
    updateAndReload,
    dismissUpdate,
  };
};   