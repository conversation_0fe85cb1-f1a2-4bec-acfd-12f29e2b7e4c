import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useCategoryQuery } from '@/queries/useCategoryQuery';

export type DealCollectionType = 
  | 'categories'
  | 'weekend'
  | 'featured'
  | 'nearby'
  | 'trending'
  | 'ending_soon'
  | 'high_discount';

interface DealItem {
  id: string;
  title: string;
  discounted_price: number;
  original_price: number;
  discount_percentage: number;
  images: string[];
  businesses: {
    name: string;
    city?: string;
  };
}

interface DealSection {
  id: string;
  name: string;
  icon?: string;
  iconType?: 'iconify' | 'lucide';
  deals: DealItem[];
  viewAllUrl?: string;
}

export const useCategorySectionsQuery = (
  userId: string | undefined,
  userPreferences: boolean = true,
  maxSections: number = 6,
  dealsPerSection: number = 10
) => {
  const { data: allCategories, isLoading: isLoadingCategories } = useCategoryQuery();

  return useQuery({
    queryKey: ['flexible-deals', 'categories', userId, userPreferences, maxSections, dealsPerSection],
    queryFn: async (): Promise<DealSection[]> => {
      if (!allCategories) return [];

      let categories = [...allCategories];
      
      if (userPreferences && userId) {
        const { data: userPrefs } = await supabase
          .from("user_preferences")
          .select("*")
          .eq("user_id", userId)
          .maybeSingle();
          
        if (userPrefs && userPrefs.categories) {
          const preferredCategories = userPrefs.categories
            .map((id: string) => allCategories.find(cat => cat.id === id))
            .filter(Boolean);
          
          if (preferredCategories.length > 0) {
            categories = preferredCategories;
          }
        }
      }

      const randomizedCategories = categories
        .sort(() => 0.5 - Math.random())
        .slice(0, maxSections);

      const sectionsData = await Promise.all(
        randomizedCategories.map(async (category) => {
          const { data: deals, error } = await supabase
            .from("deals")
            .select(`
              id,
              title,
              discounted_price,
              original_price,
              discount_percentage,
              images,
              businesses!inner(name, city)
            `)
            .eq("status", "published")
            .eq("businesses.category_id", category.id)
            .gte("end_date", new Date().toISOString())
            .order("created_at", { ascending: false })
            .limit(dealsPerSection);

          if (error) {
            throw new Error(`Errore nel recupero delle offerte per categoria ${category.name}: ${error.message}`);
          }

          return {
            id: category.id,
            name: category.name || "",
            icon: category.icon || "",
            iconType: 'iconify' as const,
            deals: deals || [],
            viewAllUrl: `/deals?category=${category.id}`
          };
        })
      );

      return sectionsData;
    },
    enabled: !!allCategories && !isLoadingCategories,
    staleTime: 1000 * 60 * 5, // 5 minutes - deals change moderately
    refetchOnWindowFocus: false,
  });
};

export const useWeekendDealsQuery = (dealsPerSection: number = 10) => {
  return useQuery({
    queryKey: ['flexible-deals', 'weekend', dealsPerSection],
    queryFn: async (): Promise<DealSection[]> => {
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 6 = Saturday
      
      if (dayOfWeek < 5 && dayOfWeek > 0) {
        return [];
      }

      const { data: deals, error } = await supabase
        .from("deals")
        .select(`
          id,
          title,
          discounted_price,
          original_price,
          discount_percentage,
          images,
          businesses!inner(name, city)
        `)
        .eq("status", "published")
        .gte("end_date", new Date().toISOString())
        .gte("discount_percentage", 20) // Weekend deals should have good discounts
        .order("discount_percentage", { ascending: false })
        .limit(dealsPerSection);

      if (error) {
        throw new Error(`Errore nel recupero delle offerte weekend: ${error.message}`);
      }

      return [{
        id: 'weekend',
        name: 'Offerte Weekend',
        icon: 'mdi:calendar-weekend',
        iconType: 'iconify',
        deals: deals || [],
        viewAllUrl: '/deals?filter=weekend'
      }];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useFeaturedDealsQuery = (dealsPerSection: number = 10) => {
  return useQuery({
    queryKey: ['flexible-deals', 'featured', dealsPerSection],
    queryFn: async (): Promise<DealSection[]> => {
      const { data: businesses, error } = await supabase
        .from("businesses")
        .select(`
          id,
          name,
          city,
          score,
          deals!inner(
            id,
            title,
            discounted_price,
            original_price,
            discount_percentage,
            images
          )
        `)
        .eq("deals.status", "published")
        .gte("deals.end_date", new Date().toISOString())
        .gte("score", 4.0) // High-rated businesses
        .order("score", { ascending: false })
        .limit(dealsPerSection);

      if (error) {
        throw new Error(`Errore nel recupero delle offerte in evidenza: ${error.message}`);
      }

      const deals = businesses?.flatMap(business => 
        business.deals?.map(deal => ({
          ...deal,
          businesses: {
            name: business.name,
            city: business.city
          }
        })) || []
      ) || [];

      return [{
        id: 'featured',
        name: 'Offerte in Evidenza',
        icon: 'mdi:star',
        iconType: 'iconify',
        deals: deals,
        viewAllUrl: '/deals?filter=featured'
      }];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useEndingSoonDealsQuery = (dealsPerSection: number = 10) => {
  return useQuery({
    queryKey: ['flexible-deals', 'ending_soon', dealsPerSection],
    queryFn: async (): Promise<DealSection[]> => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 2);

      const { data: deals, error } = await supabase
        .from("deals")
        .select(`
          id,
          title,
          discounted_price,
          original_price,
          discount_percentage,
          images,
          businesses!inner(name, city)
        `)
        .eq("status", "published")
        .gte("end_date", new Date().toISOString())
        .lte("end_date", tomorrow.toISOString())
        .order("end_date", { ascending: true })
        .limit(dealsPerSection);

      if (error) {
        throw new Error(`Errore nel recupero delle offerte in scadenza: ${error.message}`);
      }

      return [{
        id: 'ending_soon',
        name: 'Scadono Presto',
        icon: 'mdi:clock-alert',
        iconType: 'iconify',
        deals: deals || [],
        viewAllUrl: '/deals?filter=ending_soon'
      }];
    },
    staleTime: 1000 * 60 * 1, // 1 minute - ending soon deals need frequent updates
    refetchOnWindowFocus: true,
  });
};

export const useHighDiscountDealsQuery = (dealsPerSection: number = 10) => {
  return useQuery({
    queryKey: ['flexible-deals', 'high_discount', dealsPerSection],
    queryFn: async (): Promise<DealSection[]> => {
      const { data: deals, error } = await supabase
        .from("deals")
        .select(`
          id,
          title,
          discounted_price,
          original_price,
          discount_percentage,
          images,
          businesses!inner(name, city)
        `)
        .eq("status", "published")
        .gte("end_date", new Date().toISOString())
        .gte("discount_percentage", 40) // High discount threshold
        .order("discount_percentage", { ascending: false })
        .limit(dealsPerSection);

      if (error) {
        throw new Error(`Errore nel recupero delle offerte ad alto sconto: ${error.message}`);
      }

      return [{
        id: 'high_discount',
        name: 'Super Sconti',
        icon: 'mdi:percent',
        iconType: 'iconify',
        deals: deals || [],
        viewAllUrl: '/deals?filter=high_discount'
      }];
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

export const useFlexibleDealsQuery = (
  collectionType: DealCollectionType,
  options: {
    maxSections?: number;
    dealsPerSection?: number;
    userPreferences?: boolean;
    userId?: string;
  } = {}
) => {
  const {
    maxSections = 6,
    dealsPerSection = 10,
    userPreferences = true,
    userId
  } = options;

  const categorySections = useCategorySectionsQuery(userId, userPreferences, maxSections, dealsPerSection);
  const weekendDeals = useWeekendDealsQuery(dealsPerSection);
  const featuredDeals = useFeaturedDealsQuery(dealsPerSection);
  const endingSoonDeals = useEndingSoonDealsQuery(dealsPerSection);
  const highDiscountDeals = useHighDiscountDealsQuery(dealsPerSection);

  switch (collectionType) {
    case 'categories':
      return categorySections;
    case 'weekend':
      return weekendDeals;
    case 'featured':
      return featuredDeals;
    case 'ending_soon':
      return endingSoonDeals;
    case 'high_discount':
      return highDiscountDeals;
    default:
      return { data: [], isLoading: false, error: null };
  }
};
