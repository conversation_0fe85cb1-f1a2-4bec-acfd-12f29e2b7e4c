import { formatCurrency } from '@/lib/format-currency';
import { motion } from 'framer-motion';
import { Sparkles } from 'lucide-react';

export interface DealCardProps {
  id: string;
  images: string[];
  title: string;
  discountPercentage: number;
  matchScore?: number;
  categoryName: string;
  businessName: string;
  discountedPrice: number;
  originalPrice: number;
  onClick?: () => void;
}

export const QuickDealCard: React.FC<DealCardProps> = ({
  id,
  images,
  title,
  discountPercentage,
  matchScore,
  categoryName,
  businessName,
  discountedPrice,
  originalPrice,
  onClick,
}) => {
  return (
    <motion.div
      className="flex-shrink-0 w-72 cursor-pointer"
      whileHover={{ scale: 1.02 }}
      onClick={onClick}
    >
      <div className="bg-white rounded-xl shadow-md overflow-hidden h-full">
        <div className="relative h-36">
          <img
            src={images[0] || "https://via.placeholder.com/300x200"}
            alt={title}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-2 right-2 bg-brand-primary text-white px-2 py-1 rounded-full text-xs font-bold">
            -{discountPercentage}%
          </div>
          {matchScore && matchScore > 70 && (
            <div className="absolute bottom-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              Match {matchScore}%
            </div>
          )}
        </div>
        <div className="p-4">
          <div className="text-xs text-gray-500 mb-1">{categoryName}</div>
          <h3 className="font-semibold text-gray-800 mb-1 line-clamp-2">{title}</h3>
          <p className="text-sm text-gray-600 mb-2 line-clamp-1">{businessName}</p>
          <div className="flex items-center justify-between">
            <div>
              <span className="text-brand-primary font-bold">
                {formatCurrency(discountedPrice)}
              </span>
              <span className="text-gray-400 text-sm line-through ml-2">
                {formatCurrency(originalPrice)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}; 