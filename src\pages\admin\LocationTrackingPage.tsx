import React, { useState, useEffect, useRef } from 'react';
import { toast } from 'sonner';
import { MapPin, Users, Clock, Filter, AlertCircle, Plus, Pencil, Trash2, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

import { useAuth } from '@/hooks/auth/useAuth';
import { useLocationHistory, LocationHistoryData } from '@/hooks/location/useLocationHistory';
import { Navigate, useNavigate } from 'react-router-dom';
import UnifiedHeader from '@/components/toolbars/UnifiedHeader';

declare global {
  interface Window {
    google: any;
  }
}

const LocationTrackingPage = () => {
  const { user, isAuthenticated } = useAuth();
  const {
    locationData,
    users,
    isLoading,
    fetchLocationData,
    createLocationRecord,
    updateLocationRecord,
    deleteLocationRecord,
    deleteUserLocationHistory
  } = useLocationHistory();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedUser, setSelectedUser] = useState<string>('all');
  const [map, setMap] = useState<any>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  const [currentData, setCurrentData] = useState<LocationHistoryData[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<LocationHistoryData | null>(null);
  const [formData, setFormData] = useState({
    user_id: '',
    latitude: '',
    longitude: '',
    recorded_at: '',
    source: 'manual'
  });
  const mapContainer = useRef<HTMLDivElement>(null);

  const isAdmin = user?.user_metadata?.role === 'admin';
  const navigate = useNavigate();


  // Initialize Google Maps with proper cleanup
  useEffect(() => {
    let isMounted = true;
    let mapInstance: any = null;

    const loadGoogleMaps = async () => {
      try {
        // Check if Google Maps is already loaded
        if (window.google?.maps && mapContainer.current) {
          initMap();
          return;
        }

        // Import supabase client for edge function call
        const { supabase } = await import('@/integrations/supabase/client');

        // Call the edge function using Supabase client
        const { data, error } = await supabase.functions.invoke('get-google-maps-key');

        if (error) {
          console.error('Error fetching Google Maps API key:', error);
          return;
        }

        const GOOGLE_MAPS_API_KEY = data?.GOOGLE_MAPS_API_KEY;

        if (!GOOGLE_MAPS_API_KEY) {
          console.error('Google Maps API key not configured');
          return;
        }

        // Check if script is already being loaded or loaded
        if (document.querySelector(`script[src*="maps.googleapis.com"]`)) {
          // Wait for existing script to load
          const checkGoogleMaps = () => {
            if (window.google?.maps && isMounted) {
              initMap();
            } else if (isMounted) {
              setTimeout(checkGoogleMaps, 100);
            }
          };
          checkGoogleMaps();
          return;
        }

        // Load Google Maps script
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places`;
        script.async = true;
        script.defer = true;

        script.onload = () => {
          if (isMounted) {
            initMap();
          }
        };

        script.onerror = () => {
          console.error('Failed to load Google Maps API');
        };

        document.head.appendChild(script);

      } catch (error) {
        console.error('Error loading Google Maps:', error);
      }
    };

    const initMap = () => {
      if (!isMounted || !mapContainer.current || !window.google?.maps) return;

      try {
        // Clean up existing map if any
        if (mapInstance) {
          mapInstance = null;
        }

        mapInstance = new window.google.maps.Map(mapContainer.current, {
          zoom: 10,
          center: { lat: 45.4642, lng: 9.1900 }, // Milano center
          mapTypeId: 'roadmap',
        });

        setMap(mapInstance);
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    loadGoogleMaps();

    // Cleanup function
    return () => {
      isMounted = false;
      // Clean up map instance
      if (mapInstance) {
        try {
          // Clear any listeners and objects
          window.google?.maps?.event?.clearInstanceListeners?.(mapInstance);
          mapInstance = null;
        } catch (error) {
          console.warn('Error cleaning up map:', error);
        }
      }
      // Clean up markers array
      setMarkers([]);
      setMap(null);
    };
  }, []);

  // Update map with polylines connecting location points
  const updateMapMarkers = (data: LocationHistoryData[]) => {
    if (!map || !window.google?.maps) return;

    // Safely clear existing markers and polylines with better error handling
    if (markers.length > 0) {
      markers.forEach((marker, index) => {
        try {
          if (marker && typeof marker.setMap === 'function') {
            marker.setMap(null);
          }
          // Also try to clear any event listeners
          if (window.google?.maps?.event?.clearInstanceListeners) {
            window.google.maps.event.clearInstanceListeners(marker);
          }
        } catch (error) {
          console.warn(`Error removing marker at index ${index}:`, error);
        }
      });
    }

    // Clear the markers array immediately
    setMarkers([]);

    const newMarkers: any[] = [];

    if (data.length === 0) return;

    const bounds = new window.google.maps.LatLngBounds();

    // Color palette for different users
    const colors = ['#1E40AF', '#DC2626', '#059669', '#D97706', '#7C3AED', '#DB2777', '#0891B2'];
    const userColors: { [key: string]: string } = {};

    // Group data by user
    const userGroups: { [key: string]: LocationHistoryData[] } = {};
    data.forEach((location) => {
      if (!userGroups[location.user_id]) {
        userGroups[location.user_id] = [];
      }
      userGroups[location.user_id].push(location);
    });

    // Create polylines for each user
    Object.entries(userGroups).forEach(([userId, locations], index) => {
      try {
        // Assign color to user if not already assigned
        if (!userColors[userId]) {
          userColors[userId] = colors[index % colors.length];
        }

        // Sort locations by timestamp
        const sortedLocations = locations.sort((a, b) =>
          new Date(a.recorded_at).getTime() - new Date(b.recorded_at).getTime()
        );

        // Create path for polyline
        const path = sortedLocations.map(location => ({
          lat: location.latitude,
          lng: location.longitude
        }));

        // Create polyline
        const polyline = new window.google.maps.Polyline({
          path: path,
          geodesic: true,
          strokeColor: userColors[userId],
          strokeOpacity: 1.0,
          strokeWeight: 3,
          map: map
        });

        newMarkers.push(polyline);

        // Add markers for start and end points
        if (sortedLocations.length > 0) {
          const startLocation = sortedLocations[0];
          const endLocation = sortedLocations[sortedLocations.length - 1];

          // Start marker
          const startMarker = new window.google.maps.Marker({
            position: { lat: startLocation.latitude, lng: startLocation.longitude },
            map: map,
            title: `Inizio - ${startLocation.user_email}`,
            icon: {
              path: window.google.maps.SymbolPath.CIRCLE,
              scale: 8,
              fillColor: userColors[userId],
              fillOpacity: 1,
              strokeColor: '#FFFFFF',
              strokeWeight: 2
            }
          });

          // End marker
          const endMarker = new window.google.maps.Marker({
            position: { lat: endLocation.latitude, lng: endLocation.longitude },
            map: map,
            title: `Fine - ${endLocation.user_email}`,
            icon: {
              path: window.google.maps.SymbolPath.CIRCLE,
              scale: 10,
              fillColor: userColors[userId],
              fillOpacity: 1,
              strokeColor: '#FFFFFF',
              strokeWeight: 3
            }
          });

          // Info windows
          const startInfo = new window.google.maps.InfoWindow({
            content: `
              <div>
                <h3>🟢 Inizio percorso</h3>
                <p><strong>Utente:</strong> ${startLocation.user_email}</p>
                <p><strong>Orario:</strong> ${new Date(startLocation.recorded_at).toLocaleString()}</p>
                <p><strong>Coordinate:</strong> ${startLocation.latitude.toFixed(6)}, ${startLocation.longitude.toFixed(6)}</p>
              </div>
            `
          });

          const endInfo = new window.google.maps.InfoWindow({
            content: `
              <div>
                <h3>🔴 Fine percorso</h3>
                <p><strong>Utente:</strong> ${endLocation.user_email}</p>
                <p><strong>Orario:</strong> ${new Date(endLocation.recorded_at).toLocaleString()}</p>
                <p><strong>Coordinate:</strong> ${endLocation.latitude.toFixed(6)}, ${endLocation.longitude.toFixed(6)}</p>
                <p><strong>Punti totali:</strong> ${sortedLocations.length}</p>
              </div>
            `
          });

          startMarker.addListener('click', () => {
            startInfo.open(map, startMarker);
          });

          endMarker.addListener('click', () => {
            endInfo.open(map, endMarker);
          });

          newMarkers.push(startMarker, endMarker);
        }

        // Extend bounds for all points
        sortedLocations.forEach(location => {
          bounds.extend({ lat: location.latitude, lng: location.longitude });
        });

      } catch (error) {
        console.error('Error creating polyline for user:', userId, error);
      }
    });

    setMarkers(newMarkers);

    // Fit map to show all paths
    if (newMarkers.length > 0) {
      try {
        map.fitBounds(bounds);
      } catch (error) {
        console.warn('Error fitting map bounds:', error);
      }
    }
  };

  // Fetch and update data
  const handleFetchData = async () => {
    try {
      const data = await fetchLocationData(selectedDate, selectedUser);
      setCurrentData(data);
      updateMapMarkers(data);
    } catch (error) {
      toast.error('Errore nel caricamento dei dati');
    }
  };

  // Load data when filters change
  useEffect(() => {
    if (users.length > 0) {
      handleFetchData();
    }
  }, [selectedDate, selectedUser, users]);

  // CRUD operations
  const handleCreate = async () => {
    try {
      const record = {
        user_id: formData.user_id,
        latitude: parseFloat(formData.latitude),
        longitude: parseFloat(formData.longitude),
        recorded_at: formData.recorded_at || new Date().toISOString(),
        source: formData.source
      };

      const result = await createLocationRecord(record);
      if (result) {
        toast.success('Record creato con successo');
        setIsCreateDialogOpen(false);
        setFormData({ user_id: '', latitude: '', longitude: '', recorded_at: '', source: 'manual' });
        handleFetchData();
      } else {
        toast.error('Errore nella creazione del record');
      }
    } catch (error) {
      toast.error('Errore nella creazione del record');
    }
  };

  const handleEdit = (record: LocationHistoryData) => {
    setEditingRecord(record);
    setFormData({
      user_id: record.user_id,
      latitude: record.latitude.toString(),
      longitude: record.longitude.toString(),
      recorded_at: record.recorded_at,
      source: record.source
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdate = async () => {
    if (!editingRecord) return;

    try {
      const updates = {
        user_id: formData.user_id,
        latitude: parseFloat(formData.latitude),
        longitude: parseFloat(formData.longitude),
        recorded_at: formData.recorded_at,
        source: formData.source
      };

      const result = await updateLocationRecord(editingRecord.id, updates);
      if (result) {
        toast.success('Record aggiornato con successo');
        setIsEditDialogOpen(false);
        setEditingRecord(null);
        setFormData({ user_id: '', latitude: '', longitude: '', recorded_at: '', source: 'manual' });
        handleFetchData();
      } else {
        toast.error('Errore nell\'aggiornamento del record');
      }
    } catch (error) {
      toast.error('Errore nell\'aggiornamento del record');
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Sei sicuro di voler eliminare questo record?')) return;

    try {
      const result = await deleteLocationRecord(id);
      if (result) {
        toast.success('Record eliminato con successo');
        handleFetchData();
      } else {
        toast.error('Errore nell\'eliminazione del record');
      }
    } catch (error) {
      toast.error('Errore nell\'eliminazione del record');
    }
  };

  const handleDeleteUserHistory = async (userId: string, userEmail: string) => {
    if (!confirm(`Sei sicuro di voler eliminare tutti i dati di ${userEmail}?`)) return;

    try {
      const result = await deleteUserLocationHistory(userId);
      if (result) {
        toast.success('Storico utente eliminato con successo');
        handleFetchData();
      } else {
        toast.error('Errore nell\'eliminazione dello storico utente');
      }
    } catch (error) {
      toast.error('Errore nell\'eliminazione dello storico utente');
    }
  };

  // Get stats
  const getStats = () => {
    const uniqueUsers = new Set(currentData.map(item => item.user_id)).size;
    const totalPoints = currentData.length;
    const timeRange = currentData.length > 0 ? {
      first: new Date(currentData[0].recorded_at).toLocaleTimeString(),
      last: new Date(currentData[currentData.length - 1].recorded_at).toLocaleTimeString()
    } : null;

    return { uniqueUsers, totalPoints, timeRange };
  };

  // Show loading if authentication is still being checked
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          <p className="mt-4 text-lg">Caricamento...</p>
        </div>
      </div>
    );
  }

  // Redirect if not admin
  if (!isAdmin) {
    return <Navigate to="/profile" replace />;
  }

  const stats = getStats();

  return (
    <div className="min-h-screen bg-background">
      <UnifiedHeader showBackButton={true} onBack={() => navigate(-1)} title="Tracking Dati Posizione" isBusiness={false} />

      <main className="pt-16 p-4 space-y-6">
        {/* Google Maps API Key Warning */}
        {!window.google && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Per visualizzare la mappa è necessario configurare la chiave API di Google Maps nelle impostazioni del progetto.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filtri
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Data</label>
                <Input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Utente</label>
                <Select value={selectedUser} onValueChange={setSelectedUser}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona utente" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tutti gli utenti</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button onClick={handleFetchData} disabled={isLoading} className="w-full">
              {isLoading ? 'Caricamento...' : 'Aggiorna Dati'}
            </Button>
          </CardContent>
        </Card>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="flex items-center gap-2 p-4">
              <Users className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Utenti Tracciati</p>
                <p className="text-2xl font-bold">{stats.uniqueUsers}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center gap-2 p-4">
              <MapPin className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Punti Totali</p>
                <p className="text-2xl font-bold">{stats.totalPoints}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center gap-2 p-4">
              <Clock className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Intervallo Tempo</p>
                <p className="text-sm font-medium">
                  {stats.timeRange ? `${stats.timeRange.first} - ${stats.timeRange.last}` : 'N/A'}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Map */}
        <Card>
          <CardHeader>
            <CardTitle>Mappa Posizioni</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              ref={mapContainer}
              className="w-full h-96 bg-muted rounded-lg flex items-center justify-center"
              style={{ minHeight: '400px' }}
            >
              {!window.google && (
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">
                    Mappa non disponibile - Configurare Google Maps API Key
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Data Table with CRUD */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Dati Dettagliati</CardTitle>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Aggiungi Record
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Crea Nuovo Record</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="user_id">Utente</Label>
                    <Select value={formData.user_id} onValueChange={(value) => setFormData({...formData, user_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleziona utente" />
                      </SelectTrigger>
                      <SelectContent>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="latitude">Latitudine</Label>
                    <Input
                      id="latitude"
                      type="number"
                      step="any"
                      value={formData.latitude}
                      onChange={(e) => setFormData({...formData, latitude: e.target.value})}
                      placeholder="45.464664"
                    />
                  </div>
                  <div>
                    <Label htmlFor="longitude">Longitudine</Label>
                    <Input
                      id="longitude"
                      type="number"
                      step="any"
                      value={formData.longitude}
                      onChange={(e) => setFormData({...formData, longitude: e.target.value})}
                      placeholder="9.190000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="recorded_at">Data e Ora</Label>
                    <Input
                      id="recorded_at"
                      type="datetime-local"
                      value={formData.recorded_at}
                      onChange={(e) => setFormData({...formData, recorded_at: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="source">Fonte</Label>
                    <Select value={formData.source} onValueChange={(value) => setFormData({...formData, source: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manual">Manuale</SelectItem>
                        <SelectItem value="automatic">Automatico</SelectItem>
                        <SelectItem value="real_during_demo">Real Durante Demo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button onClick={handleCreate} className="w-full">
                    Crea Record
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </CardHeader>
          <CardContent>
            {currentData.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Utente</TableHead>
                      <TableHead>Coordinate</TableHead>
                      <TableHead>Data/Ora</TableHead>
                      <TableHead>Fonte</TableHead>
                      <TableHead>Azioni</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentData.map((location) => (
                      <TableRow key={location.id}>
                        <TableCell className="font-medium">{location.user_email}</TableCell>
                        <TableCell className="text-sm">
                          {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
                        </TableCell>
                        <TableCell className="text-sm">
                          {new Date(location.recorded_at).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <span className="text-xs bg-secondary px-2 py-1 rounded">{location.source}</span>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem onClick={() => handleEdit(location)}>
                                <Pencil className="h-4 w-4 mr-2" />
                                Modifica
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDelete(location.id)}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Elimina
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => handleDeleteUserHistory(location.user_id, location.user_email || '')}
                                className="text-destructive"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Elimina Storico Utente
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <p className="text-center text-muted-foreground py-8">
                Nessun dato trovato per la data selezionata
              </p>
            )}
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Modifica Record</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit_user_id">Utente</Label>
                <Select value={formData.user_id} onValueChange={(value) => setFormData({...formData, user_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seleziona utente" />
                  </SelectTrigger>
                  <SelectContent>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="edit_latitude">Latitudine</Label>
                <Input
                  id="edit_latitude"
                  type="number"
                  step="any"
                  value={formData.latitude}
                  onChange={(e) => setFormData({...formData, latitude: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit_longitude">Longitudine</Label>
                <Input
                  id="edit_longitude"
                  type="number"
                  step="any"
                  value={formData.longitude}
                  onChange={(e) => setFormData({...formData, longitude: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit_recorded_at">Data e Ora</Label>
                <Input
                  id="edit_recorded_at"
                  type="datetime-local"
                  value={formData.recorded_at ? new Date(formData.recorded_at).toISOString().slice(0, 16) : ''}
                  onChange={(e) => setFormData({...formData, recorded_at: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="edit_source">Fonte</Label>
                <Select value={formData.source} onValueChange={(value) => setFormData({...formData, source: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">Manuale</SelectItem>
                    <SelectItem value="automatic">Automatico</SelectItem>
                    <SelectItem value="real_during_demo">Real Durante Demo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleUpdate} className="w-full">
                Aggiorna Record
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </main>
    </div>
  );
};

export default LocationTrackingPage;