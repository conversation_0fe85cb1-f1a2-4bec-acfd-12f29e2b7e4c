import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useLocationManagement } from "./location/useLocationManagement";
import { useAvailabilitySearchQuery, type AvailabilitySearchParams } from "@/queries/useAvailabilitySearchQuery";


export interface BusinessAvailability {
  business_id: string;
  business_name: string;
  address: string;
  city: string;
  zip_code: string;
  state: string;
  country: string;
  latitude: number;
  longitude: number;
  distance: number;
  category_id: string;
  available_slots: any;
  start_date: string;
  end_date: string;
  photos?: string[];
}

export interface DaySchedule {
  day: number;
  day_name: string;
  time_slots: TimeSlot[];
}

export interface TimeSlot {
  start_time: string;
  end_time: string;
  available_seats: number;
}

export interface TimeSlotQueryParams {
  date: string;
  time?: string;
  location?: {
    lat: number;
    lng: number;
    radius?: number;
  };
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}

export interface SearchResults {
  businesses: any[];
  next_cursor: string | null;
}

export interface SearchMetrics {
  queryTime: number;
  totalResults: number;
  isCached: boolean;
  pageNumber: number;
}

export function useAvailabilitySearch(
  initialParams?: Partial<TimeSlotQueryParams>
) {
  const [params, setParams] = useState<TimeSlotQueryParams>({
    date: initialParams?.date || new Date().toISOString().split("T")[0],
    time: initialParams?.time,
    location: initialParams?.location,
    cityName: initialParams?.cityName,
    category_id: initialParams?.category_id,
    limit: initialParams?.limit || 20,
  });

  const [results, setResults] = useState<any[]>([]);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<SearchMetrics>({
    queryTime: 0,
    totalResults: 0,
    isCached: false,
    pageNumber: 1,
  });

  const { userLocation } = useLocationManagement();

  const searchLocation = params.location || (userLocation ? { lat: userLocation.lat, lng: userLocation.lng } : undefined);
  
  const { data: queryData, isLoading: queryLoading, error: queryError, refetch } = useAvailabilitySearchQuery({
    date: params.date,
    time: params.time,
    location: searchLocation,
    cityName: params.cityName,
    category_id: params.category_id,
    cursor: null, // Always start with null for base query
    limit: params.limit,
  });

  useEffect(() => {
    if (queryData) {
      setResults(queryData.businesses || []);
      setNextCursor(queryData.next_cursor);
      setMetrics(prev => ({
        ...prev,
        totalResults: queryData.businesses?.length || 0,
        isCached: queryData.cached || false,
        pageNumber: 1,
      }));
    }
  }, [queryData]);

  useEffect(() => {
    if (queryError) {
      setError(queryError.message);
      toast.error("Impossibile completare la ricerca di disponibilità");
    } else {
      setError(null);
    }
  }, [queryError]);

  useEffect(() => {
    setIsLoading(queryLoading);
  }, [queryLoading]);

  const searchAvailability = useCallback(
    async (isLoadMore: boolean = false) => {
      if (isLoadMore) {
        if (!nextCursor) return;
        
        try {
          setIsLoadingMore(true);
          const { data, error } = await supabase.functions.invoke(
            "get-availability-by-location",
            {
              body: {
                date: params.date,
                time: params.time,
                location: searchLocation,
                cityName: params.cityName,
                category_id: params.category_id,
                cursor: nextCursor,
                limit: params.limit,
              },
            }
          );

          if (error) throw new Error(error.message);

          setResults(prev => [...prev, ...data.businesses]);
          setNextCursor(data.next_cursor);
          setMetrics(prev => ({
            ...prev,
            totalResults: prev.totalResults + (data.businesses?.length || 0),
            pageNumber: prev.pageNumber + 1,
          }));
        } catch (err: any) {
          console.error("Errore nel caricamento di più risultati:", err);
          setError(err.message || "Errore durante il caricamento");
          toast.error("Impossibile caricare più risultati");
        } finally {
          setIsLoadingMore(false);
        }
      } else {
        refetch();
      }
    },
    [params, searchLocation, nextCursor, refetch]
  );

  useEffect(() => {
    searchAvailability(false);
  }, [params, userLocation]);

  const updateSearchParams = (newParams: Partial<TimeSlotQueryParams>) => {
    setParams((prev) => ({ ...prev, ...newParams }));
    setNextCursor(null); // Reset pagination when params change
  };

  const loadMore = () => {
    if (nextCursor && !isLoadingMore) {
      searchAvailability(true);
    }
  };

  const refresh = () => {
    setNextCursor(null);
    searchAvailability(false);
  };

  return {
    results,
    isLoading,
    isLoadingMore,
    error,
    params,
    metrics,
    hasMore: !!nextCursor,
    updateSearchParams,
    loadMore,
    refresh,
  };
}
