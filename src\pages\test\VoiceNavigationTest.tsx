import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useVoiceDialog } from '../../contexts/VoiceDialogContext';

const VoiceNavigationTest = () => {
  const { openVoiceDialog } = useVoiceDialog();
  const navigate = useNavigate();
  const location = useLocation();

  const testRoutes = [
    { name: 'Home', path: '/' },
    { name: 'Deals', path: '/deals' },
    { name: 'Bookings', path: '/le-mie-prenotazioni' },
    { name: 'Profile', path: '/profile' },
    { name: 'Experiences', path: '/experiences' },
    { name: 'Social', path: '/social' },
    { name: 'Map', path: '/map' },
    { name: 'Meteo', path: '/meteo' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Voice Navigation Test</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Current Location</h2>
          <p className="text-gray-600 mb-4">
            Current path: <code className="bg-gray-100 px-2 py-1 rounded">{location.pathname}</code>
          </p>
          
          <button
            onClick={openVoiceDialog}
            className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            🎤 Open Voice Assistant
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Test Navigation Commands</h2>
          <p className="text-gray-600 mb-4">
            Try saying these commands to the voice assistant:
          </p>
          <ul className="space-y-2 text-sm text-gray-700">
            <li>• "Vai alla home" or "Torna alla home"</li>
            <li>• "Vai alle offerte" or "Mostra offerte"</li>
            <li>• "Vai alle prenotazioni" or "Le mie prenotazioni"</li>
            <li>• "Vai al profilo" or "Profilo utente"</li>
            <li>• "Vai alle esperienze"</li>
            <li>• "Vai al social"</li>
            <li>• "Vai alla mappa"</li>
            <li>• "Vai al meteo"</li>
          </ul>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Manual Navigation</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {testRoutes.map((route) => (
              <button
                key={route.path}
                onClick={() => navigate(route.path)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  location.pathname === route.path
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {route.name}
              </button>
            ))}
          </div>
        </div>

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">How it works:</h3>
          <ol className="list-decimal list-inside space-y-2 text-blue-800">
            <li>Click "Open Voice Assistant" to start a voice conversation</li>
            <li>The assistant will start in full-screen conversation mode</li>
            <li>When you say a navigation command, it will automatically switch to navigation mode</li>
            <li>In navigation mode, the voice interface becomes a compact overlay</li>
            <li>You can see the application behind it while the navigation happens</li>
            <li>Click the phone icon in navigation mode to return to full conversation</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default VoiceNavigationTest;
