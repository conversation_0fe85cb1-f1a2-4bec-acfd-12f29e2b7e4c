
import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { DateAvailabilitySelector } from '@/components/DateAvailabilitySelector';
import { formatDateRange } from '@/utils/dateUtils';
import { useAuth } from "@/hooks/auth/useAuth";
import { toast } from "sonner";
import { useDealDetailsQuery } from '@/queries/useDealDetailsQuery';

interface Deal {
  id: string;
  title: string;
  description: string;
  price: number;
  original_price: number;
  image_url: string;
  business_name: string;
  business_logo?: string;
  time_slots: any;
  capacity: number;
  location: string;
  terms_conditions?: string;
}

interface DealFromDB {
  id: string;
  title: string;
  description: string;
  original_price: number;
  discounted_price: number;
  images: string[];
  business_id: string;
  time_slots: any;
  businesses?: {
    name: string;
    address: string;
  };
  // altri campi possibili
}

/**
 * Page component for displaying deal details with availability selection
 */
export function DealDetailPage() {
  const { id } = useParams<{ id: string }>();
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedAvailability, setSelectedAvailability] = useState<{
    available: number;
    capacity: number;
    booked: number;
  } | null>(null);
  const [quantity, setQuantity] = useState(1);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  
  const { data: dealData, isLoading: loading, error } = useDealDetailsQuery(id);
  
  const deal: Deal | null = dealData ? {
    id: dealData.id,
    title: dealData.title,
    description: dealData.description || '',
    price: dealData.discounted_price || 0,
    original_price: dealData.original_price || 0,
    image_url: dealData.images && dealData.images.length > 0 ? dealData.images[0] : '/placeholder.svg',
    business_name: dealData.businesses?.name || 'Business',
    time_slots: dealData.time_slots,
    capacity: 10, // Valore di default
    location: dealData.businesses?.address || 'Location not specified'
  } : null;
  
  const handleDateSelection = (date: string, availability: {
    available: number;
    capacity: number;
    booked: number;
  }) => {
    setSelectedDate(date);
    setSelectedAvailability(availability);
    
    // Reset quantity if it exceeds available seats
    if (quantity > availability.available) {
      setQuantity(Math.max(1, availability.available));
    }
  };
  
  const handleBookNow = () => {
    if (!isAuthenticated) {
      toast.error("Devi accedere per prenotare");
      navigate("/login");
      return;
    }
    
    if (!id || !selectedDate) return;
    navigate(`/book/${id}?date=${selectedDate}&quantity=${quantity}`);
  };
  
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="animate-pulse">
          <div className="w-full h-64 bg-gray-200 rounded-lg mb-6"></div>
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>
          <div className="h-40 bg-gray-200 rounded mb-6"></div>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-red-50 text-red-800 p-4 rounded-md">
          Errore nel caricamento dell'offerta
        </div>
      </div>
    );
  }
  
  if (!deal) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-red-50 text-red-800 p-4 rounded-md">
          Deal not found
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-6">
      {/* Deal header with image, title, etc. */}
      <div className="mb-6">
        <img src={deal.image_url} alt={deal.title} className="w-full h-64 object-cover rounded-lg" />
        <h1 className="text-2xl font-bold mt-4">{deal.title}</h1>
        <p className="text-gray-600">{deal.description}</p>
        <div className="flex items-center mt-2">
          <span className="text-xl font-bold">${deal.price}</span>
          <span className="text-gray-500 line-through ml-2">${deal.original_price}</span>
          <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
            {Math.round((1 - deal.price / deal.original_price) * 100)}% off
          </span>
        </div>
      </div>
      
      {/* Availability selector component */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Select Date & Check Availability</h2>
        <DateAvailabilitySelector 
          dealId={id || ''} 
          onSelectDate={handleDateSelection}
        />
        
        {selectedDate && selectedAvailability && selectedAvailability.available > 0 && (
          <div className="mt-6 pt-4 border-t">
            <h3 className="font-medium mb-2">How many seats?</h3>
            <div className="flex items-center">
              <button 
                className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center"
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                disabled={quantity <= 1}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <span className="mx-4 text-xl font-medium">{quantity}</span>
              <button 
                className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center"
                onClick={() => setQuantity(Math.min(selectedAvailability.available, quantity + 1))}
                disabled={quantity >= selectedAvailability.available}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              {selectedAvailability.booked} of {selectedAvailability.capacity} seats already booked
            </div>
            
            <button 
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg mt-6 font-medium transition-colors"
              onClick={handleBookNow}
            >
              Book Now
            </button>
          </div>
        )}
        
        {selectedDate && selectedAvailability && selectedAvailability.available === 0 && (
          <div className="mt-6 pt-4 border-t">
            <div className="p-4 bg-red-50 text-red-800 rounded-md">
              This date is fully booked. Please select another date.
            </div>
          </div>
        )}
      </div>
      
      {/* Deal details */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Details</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-700">Available Dates</h3>
            <p className="text-gray-600">{formatDateRange(deal.time_slots)}</p>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-700">Location</h3>
            <p className="text-gray-600">{deal.location}</p>
          </div>
          
          {deal.terms_conditions && (
            <div>
              <h3 className="font-medium text-gray-700">Terms & Conditions</h3>
              <p className="text-gray-600">{deal.terms_conditions}</p>
            </div>
          )}
        </div>
      </div>
      
      {/* Business information */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">About the Business</h2>
        
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-gray-200 mr-3 overflow-hidden">
            <img 
              src={deal.business_logo || '/placeholder.svg'} 
              alt={deal.business_name}
              className="w-full h-full object-cover" 
            />
          </div>
          <div>
            <h3 className="font-medium">{deal.business_name}</h3>
            <p className="text-sm text-gray-600">{deal.location}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
