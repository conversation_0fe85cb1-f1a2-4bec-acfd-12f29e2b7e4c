import { useAuth } from "@/hooks/auth/useAuth";
import { createOrGetPersonalAIAssistantChatConversation } from "@/services/chatService";
import { Brain, Calendar, Camera, Cloud, Compass, Users, CalendarDays } from "lucide-react";
import { toast } from "sonner";

export interface MenuTab {
  id: string;
  label: string;
  href?: string;
  badge?: number;
  invokeFunction?: boolean;
  invokeOnClick?: boolean;
  onClick?: () => void;
  icon: React.ComponentType<{ className?: string }>;
}

  const tabs: MenuTab[] = [
//     {
//     id: "esperienze",
//     label: "Esperienze",
//     icon: Compass
//   },
   {
    id: "assistant",
    label: "AI Assistant",
   // href: "/conversation/ai",
    invokeFunction: true,
  
   // badge: 3,
    icon: Brain
  },
   {
    id: "social",
    label: "Social",
    href: "/social",
   // badge: 3,
    icon: Users
  },
//    {
//     id: "ar",
//     label: "AR Deals",
//     icon: Camera
//   },
   {
    id: "agenda",
    label: "Agenda",
    href: "/agenda",
    icon: CalendarDays
  },
   {
    id: "meteo",
    label: "Meteo",
    href: "/meteo",
    icon: Cloud
  },
//   {
//     id: "eventi",
//     label: "Eventi",
//     badge: 2,
//     icon: Calendar
//   }

];


  export default tabs;
