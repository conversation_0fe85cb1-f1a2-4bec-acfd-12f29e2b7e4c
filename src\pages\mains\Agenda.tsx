import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { CalendarDays, Clock, Plus, Bell, AlertCircle, Check, X, Archive, Edit, RotateCcw, Calendar, List, CalendarRange } from "lucide-react";
import { format, isToday, isTomorrow, isThisWeek, isThisMonth, isPast, isFuture } from "date-fns";
import { it } from "date-fns/locale";
import { toast } from "sonner";
import CreateReminderDialog from "@/components/agenda/CreateReminderDialog";
import CalendarView from "@/components/agenda/CalendarView";
import WeekView from "@/components/agenda/WeekView";
import DayView from "@/components/agenda/DayView";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

const Agenda = () => {
  const { user } = useAuth();
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("upcoming");
  const [viewMode, setViewMode] = useState<'list' | 'month' | 'week' | 'day'>('list');

  const fetchReminders = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('reminders')
        .select('*')
        .eq('user_id', user.id)
        .order('reminder_time', { ascending: true });

      if (error) {
        console.error('Errore nel recupero dei promemoria:', error);
        return;
      }

      setReminders(data || []);
    } catch (error) {
      console.error('Errore:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReminders();
  }, [user]);

  // Smart categorization logic
  const categorizeReminders = () => {
    const now = new Date();
    
    const upcoming = reminders.filter(reminder => {
      const reminderDate = new Date(reminder.reminder_time);
      return reminder.status === 'pending' && isFuture(reminderDate);
    });
    
    const completed = reminders.filter(reminder => 
      reminder.status === 'sent'
    );
    
    const archived = reminders.filter(reminder => 
      ['cancelled', 'failed'].includes(reminder.status) || 
      (reminder.status === 'pending' && isPast(new Date(reminder.reminder_time)))
    );
    
    return { upcoming, completed, archived };
  };

  const { upcoming, completed, archived } = categorizeReminders();

  // Time-based grouping
  const groupByTime = (remindersList: Reminder[]) => {
    const groups: { [key: string]: Reminder[] } = {
      'Oggi': [],
      'Domani': [],
      'Questa Settimana': [],
      'Prossimo Mese': [],
      'Altro': []
    };

    remindersList.forEach(reminder => {
      const date = new Date(reminder.reminder_time);
      if (isToday(date)) {
        groups['Oggi'].push(reminder);
      } else if (isTomorrow(date)) {
        groups['Domani'].push(reminder);
      } else if (isThisWeek(date)) {
        groups['Questa Settimana'].push(reminder);
      } else if (isThisMonth(date)) {
        groups['Prossimo Mese'].push(reminder);
      } else {
        groups['Altro'].push(reminder);
      }
    });

    // Remove empty groups
    Object.keys(groups).forEach(key => {
      if (groups[key].length === 0) {
        delete groups[key];
      }
    });

    return groups;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'sent':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <CalendarDays className="w-4 h-4" />;
      case 'task':
        return <AlertCircle className="w-4 h-4" />;
      case 'booking':
        return <Clock className="w-4 h-4" />;
      default:
        return <Bell className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="w-4 h-4" />;
      case 'cancelled':
        return <X className="w-4 h-4" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const updateReminderStatus = async (id: string, status: 'cancelled' | 'sent' | 'pending') => {
    try {
      const { error } = await supabase
        .from('reminders')
        .update({ status })
        .eq('id', id);

      if (error) {
        toast.error('Errore nell\'aggiornamento del promemoria');
        return;
      }

      setReminders(prev => 
        prev.map(reminder => 
          reminder.id === id ? { ...reminder, status } : reminder
        )
      );

      toast.success('Promemoria aggiornato');
    } catch (error) {
      toast.error('Errore nell\'aggiornamento del promemoria');
    }
  };

  const getTimeUrgencyBadge = (time: string) => {
    const now = new Date();
    const reminderTime = new Date(time);
    const diffMinutes = Math.floor((reminderTime.getTime() - now.getTime()) / (1000 * 60));
    
    if (diffMinutes < 0) return { text: 'Scaduto', variant: 'destructive' as const };
    if (diffMinutes <= 5) return { text: 'Tra 5 min', variant: 'destructive' as const };
    if (diffMinutes <= 60) return { text: `Tra ${diffMinutes} min`, variant: 'default' as const };
    if (diffMinutes <= 1440) return { text: `Tra ${Math.floor(diffMinutes / 60)} ore`, variant: 'secondary' as const };
    
    return null;
  };

  const formatReminderTime = (time: string) => {
    try {
      const date = new Date(time);
      if (isToday(date)) {
        return format(date, "'Oggi alle' HH:mm", { locale: it });
      } else if (isTomorrow(date)) {
        return format(date, "'Domani alle' HH:mm", { locale: it });
      }
      return format(date, "dd MMM yyyy 'alle' HH:mm", { locale: it });
    } catch (error) {
      return time;
    }
  };

  const isOverdue = (time: string) => {
    return new Date(time) < new Date();
  };

  const renderReminderCard = (reminder: Reminder, showActions: boolean = true) => {
    const urgencyBadge = getTimeUrgencyBadge(reminder.reminder_time);
    
    return (
      <Card 
        key={reminder.id}
        className={`transition-all hover:shadow-md ${
          isOverdue(reminder.reminder_time) && reminder.status === 'pending' 
            ? 'border-red-200 bg-red-50' 
            : ''
        }`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <div className="mt-1">
                {getTypeIcon(reminder.type)}
              </div>
              <div className="flex-1">
                <CardTitle className="text-lg leading-tight">{reminder.title}</CardTitle>
                {reminder.description && (
                  <CardDescription className="mt-1">
                    {reminder.description}
                  </CardDescription>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-2">
              <Badge 
                variant="outline" 
                className={`${getStatusColor(reminder.status)} border`}
              >
                <span className="flex items-center space-x-1">
                  {getStatusIcon(reminder.status)}
                  <span className="capitalize">
                    {reminder.status === 'pending' ? 'In Attesa' :
                     reminder.status === 'sent' ? 'Completato' :
                     reminder.status === 'cancelled' ? 'Annullato' :
                     reminder.status === 'failed' ? 'Fallito' : reminder.status}
                  </span>
                </span>
              </Badge>
              {urgencyBadge && (
                <Badge variant={urgencyBadge.variant} className="text-xs">
                  {urgencyBadge.text}
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{formatReminderTime(reminder.reminder_time)}</span>
              </div>
              <Badge variant="secondary" className="text-xs capitalize">
                {reminder.type === 'appointment' ? 'Appuntamento' :
                 reminder.type === 'task' ? 'Attività' :
                 reminder.type === 'booking' ? 'Prenotazione' : 
                 reminder.type}
              </Badge>
            </div>
            
            {showActions && (
              <div className="flex flex-wrap gap-1 mt-2">
                {reminder.status === 'pending' && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateReminderStatus(reminder.id, 'sent')}
                      className="h-7 px-2 text-xs"
                    >
                      <Check className="w-3 h-3 mr-1" />
                      OK
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateReminderStatus(reminder.id, 'cancelled')}
                      className="h-7 px-2 text-xs"
                    >
                      <X className="w-3 h-3 mr-1" />
                      Annulla
                    </Button>
                  </>
                )}
                {reminder.status === 'sent' && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateReminderStatus(reminder.id, 'pending')}
                    className="h-7 px-2 text-xs"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    Ripristina
                  </Button>
                )}
                {(['cancelled', 'failed'].includes(reminder.status) || isOverdue(reminder.reminder_time)) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateReminderStatus(reminder.id, 'pending')}
                    className="h-7 px-2 text-xs"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    Ripristina
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderGroupedReminders = (remindersList: Reminder[]) => {
    if (remindersList.length === 0) return null;
    
    const groups = groupByTime(remindersList);
    
    return (
      <div className="space-y-6">
        {Object.entries(groups).map(([groupName, groupReminders]) => (
          <div key={groupName}>
            <h3 className="text-lg font-semibold mb-3 text-gray-900">{groupName}</h3>
            <div className="space-y-3">
              {groupReminders.map(reminder => renderReminderCard(reminder))}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderEmptyState = (tab: string) => {
    const emptyStates = {
      upcoming: {
        icon: CalendarDays,
        title: "Nessun promemoria programmato",
        description: "Non hai promemoria in arrivo. Crea il tuo primo promemoria per iniziare!",
        action: true
      },
      completed: {
        icon: Check,
        title: "Nessuna attività completata",
        description: "Quando completi i tuoi promemoria, li vedrai qui. Continua così!",
        action: false
      },
      archived: {
        icon: Archive,
        title: "Archivio vuoto",
        description: "I promemoria annullati o scaduti vengono archiviati automaticamente qui.",
        action: false
      }
    };

    const state = emptyStates[tab as keyof typeof emptyStates];
    const IconComponent = state.icon;

    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <IconComponent className="w-12 h-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">{state.title}</h3>
          <p className="text-muted-foreground text-center mb-4 max-w-md">
            {state.description}
          </p>
          {state.action && <CreateReminderDialog onReminderCreated={fetchReminders} />}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Mobile-Optimized Header */}
      <div className="flex items-center justify-between mb-3 px-1">
        <div>
          <h1 className="text-lg font-bold text-gray-900">Agenda</h1>
        </div>
        <div className="flex items-center space-x-1 flex-shrink-0">
          <div className="flex bg-muted rounded-lg p-0.5">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="h-7 w-7 p-0"
            >
              <List className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'month' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('month')}
              className="h-7 w-7 p-0"
            >
              <Calendar className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'week' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('week')}
              className="h-7 w-7 p-0"
            >
              <CalendarRange className="h-3 w-3" />
            </Button>
            <Button
              variant={viewMode === 'day' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('day')}
              className="h-7 w-7 p-0"
            >
              <CalendarDays className="h-3 w-3" />
            </Button>
          </div>
          <div className="flex-shrink-0">
            <CreateReminderDialog onReminderCreated={fetchReminders} />
          </div>
        </div>
      </div>

      {viewMode === 'month' ? (
        <CalendarView 
          reminders={reminders} 
          onReminderClick={(reminder) => {
            console.log('Reminder clicked:', reminder);
          }}
          onAddReminder={() => {
            console.log('Add reminder clicked');
          }}
        />
      ) : viewMode === 'week' ? (
        <WeekView 
          reminders={reminders}
          onReminderClick={(reminder) => {
            console.log('Reminder clicked:', reminder);
          }}
        />
      ) : viewMode === 'day' ? (
        <DayView 
          reminders={reminders}
          onReminderClick={(reminder) => {
            console.log('Reminder clicked:', reminder);
          }}
          onAddReminder={() => {
            console.log('Add reminder clicked');
          }}
        />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upcoming" className="relative text-xs px-2">
              Prossimi
              {upcoming.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-4 min-w-[16px] text-[10px] px-1">
                  {upcoming.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="completed" className="relative text-xs px-2">
              Completati
              {completed.length > 0 && (
                <Badge variant="secondary" className="ml-1 h-4 min-w-[16px] text-[10px] px-1">
                  {completed.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="archived" className="relative">
              Archiviati
              {archived.length > 0 && (
                <Badge variant="secondary" className="ml-2 h-5 min-w-[20px] text-xs">
                  {archived.length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="mt-6">
            {upcoming.length === 0 ? (
              renderEmptyState('upcoming')
            ) : (
              <>
                {isToday(new Date()) && upcoming.some(r => isToday(new Date(r.reminder_time))) && (
                  <Card className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <CardHeader>
                      <CardTitle className="text-lg text-blue-900">Riepilogo di Oggi</CardTitle>
                      <CardDescription className="text-blue-700">
                        Hai {upcoming.filter(r => isToday(new Date(r.reminder_time))).length} promemoria oggi
                      </CardDescription>
                    </CardHeader>
                  </Card>
                )}
                {renderGroupedReminders(upcoming)}
              </>
            )}
          </TabsContent>

          <TabsContent value="completed" className="mt-6">
            {completed.length === 0 ? (
              renderEmptyState('completed')
            ) : (
              <div className="space-y-4">
                {completed.sort((a, b) => new Date(b.reminder_time).getTime() - new Date(a.reminder_time).getTime())
                  .map(reminder => renderReminderCard(reminder, false))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="archived" className="mt-6">
            {archived.length === 0 ? (
              renderEmptyState('archived')
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-4">
                  <p className="text-sm text-muted-foreground">
                    Promemoria annullati, falliti o scaduti ({archived.length})
                  </p>
                </div>
                {archived.sort((a, b) => new Date(b.reminder_time).getTime() - new Date(a.reminder_time).getTime())
                  .map(reminder => (
                    <div key={reminder.id} className="opacity-75">
                      {renderReminderCard(reminder)}
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default Agenda;