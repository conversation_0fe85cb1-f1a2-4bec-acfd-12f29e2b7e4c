-- Remove business_id from integration_authorizations and tighten constraints + RLS
BEGIN;

-- 1) Ensure table exists with required columns
CREATE TABLE IF NOT EXISTS public.integration_authorizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  provider TEXT NOT NULL,
  integration_id TEXT,
  status TEXT NOT NULL DEFAULT 'disconnected',
  is_active BOOLEAN NOT NULL DEFAULT false,
  metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- 2) Add any missing columns safely (no-ops if they already exist)
ALTER TABLE public.integration_authorizations
  ADD COLUMN IF NOT EXISTS user_id UUID,
  ADD COLUMN IF NOT EXISTS provider TEXT,
  ADD COLUMN IF NOT EXISTS integration_id TEXT,
  ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'disconnected',
  ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT false,
  ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb,
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT now(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT now();

-- 3) Drop deprecated column
ALTER TABLE public.integration_authorizations
  DROP COLUMN IF EXISTS business_id;

-- 4) Strengthen constraints and defaults
ALTER TABLE public.integration_authorizations
  ALTER COLUMN provider SET NOT NULL,
  ALTER COLUMN is_active SET NOT NULL,
  ALTER COLUMN status SET NOT NULL,
  ALTER COLUMN metadata SET NOT NULL,
  ALTER COLUMN created_at SET NOT NULL,
  ALTER COLUMN updated_at SET NOT NULL,
  ALTER COLUMN created_at SET DEFAULT now(),
  ALTER COLUMN updated_at SET DEFAULT now(),
  ALTER COLUMN status SET DEFAULT 'disconnected',
  ALTER COLUMN is_active SET DEFAULT false;

-- 5) Unique constraint on (user_id, provider)
CREATE UNIQUE INDEX IF NOT EXISTS integration_authorizations_user_provider_uidx
  ON public.integration_authorizations (user_id, provider);

-- 6) RLS: enable and define policies
ALTER TABLE public.integration_authorizations ENABLE ROW LEVEL SECURITY;

-- Drop old policies if present to avoid duplicates
DROP POLICY IF EXISTS "Users can view their own integrations" ON public.integration_authorizations;
DROP POLICY IF EXISTS "Users can insert their own integrations" ON public.integration_authorizations;
DROP POLICY IF EXISTS "Users can update their own integrations" ON public.integration_authorizations;
DROP POLICY IF EXISTS "Users can delete their own integrations" ON public.integration_authorizations;

-- Create fresh policies
CREATE POLICY "Users can view their own integrations"
ON public.integration_authorizations
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own integrations"
ON public.integration_authorizations
FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own integrations"
ON public.integration_authorizations
FOR UPDATE
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own integrations"
ON public.integration_authorizations
FOR DELETE
USING (auth.uid() = user_id);

-- 7) Keep updated_at fresh via trigger
-- Function exists in project: public.update_integration_authorizations_updated_at()
DROP TRIGGER IF EXISTS trg_update_integration_authorizations_updated_at ON public.integration_authorizations;
CREATE TRIGGER trg_update_integration_authorizations_updated_at
BEFORE UPDATE ON public.integration_authorizations
FOR EACH ROW
EXECUTE FUNCTION public.update_integration_authorizations_updated_at();

COMMIT;