import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface LocationHistoryData {
  id: string;
  user_id: string;
  latitude: number;
  longitude: number;
  recorded_at: string;
  source: string;
  user_email?: string;
}

export interface UserData {
  id: string;
  email: string;
}

export const useLocationUsersQuery = () => {
  return useQuery({
    queryKey: ['location-users'],
    queryFn: async (): Promise<UserData[]> => {
      const { data, error } = await supabase
        .from('user_location_history')
        .select('user_id');

      if (error) {
        throw new Error(`Errore nel recupero degli utenti dalla cronologia: ${error.message}`);
      }

      if (!data || data.length === 0) {
        return [];
      }

      const uniqueUserIds = [...new Set(data.map(item => item.user_id))];
      
      const { data: userDetails, error: userError } = await supabase
        .from('user_details')
        .select('id, email')
        .in('id', uniqueUserIds);

      if (userError || !userDetails || userDetails.length === 0) {
        const mockUsers = uniqueUserIds.map(id => ({
          id: String(id),
          email: `utente-${String(id).substring(0, 8)}`
        }));
        return mockUsers;
      }

      const usersWithEmails = userDetails.map(user => ({
        id: user.id,
        email: user.email || `utente-${user.id.substring(0, 8)}`
      }));

      return usersWithEmails;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes - user data changes less frequently
    refetchOnWindowFocus: false,
  });
};

export const useLocationDataQuery = (
  selectedDate: string,
  selectedUser: string = 'all',
  users: UserData[] = []
) => {
  return useQuery({
    queryKey: ['location-data', selectedDate, selectedUser],
    queryFn: async (): Promise<LocationHistoryData[]> => {
      let query = supabase
        .from('user_location_history')
        .select('*')
        .gte('recorded_at', `${selectedDate}T00:00:00`)
        .lt('recorded_at', `${selectedDate}T23:59:59`)
        .order('recorded_at', { ascending: true });

      if (selectedUser !== 'all') {
        query = query.eq('user_id', selectedUser);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Errore nel recupero dei dati di posizione: ${error.message}`);
      }

      const enhancedData = (data || []).map((location) => {
        const user = users.find(u => u.id === location.user_id);
        return {
          ...location,
          user_email: user?.email || `utente-${location.user_id.substring(0, 8)}`
        };
      });

      return enhancedData;
    },
    enabled: !!selectedDate && users.length > 0,
    staleTime: 1000 * 30, // 30 seconds - location data needs frequent updates
    refetchOnWindowFocus: true,
  });
};
