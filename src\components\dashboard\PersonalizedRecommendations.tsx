import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { formatCurrency } from "@/lib/format-currency";
import { Sparkles } from "lucide-react";
import { QuickDealCard } from '@/components/deals/QuickDealCard';
import { useDragScroll } from "@/hooks/useDragScroll";

const PersonalizedRecommendations = () => {
  const navigate = useNavigate();
  const { recommendedDeals, isLoading } = useUserPreferences();
  const dragRef = useDragScroll<HTMLDivElement>();

  if (isLoading) {
    return (
      <section className="mt-6">
        <div className="flex items-center gap-2 mb-4">
          <h2 className="text-xl font-bold text-gray-800">Per Te</h2>
          <Sparkles className="h-5 w-5 text-yellow-500" />
        </div>
        <div className="flex space-x-4 overflow-x-auto pb-4">
          {[1, 2, 3].map((item) => (
            <div key={item} className="flex-shrink-0 w-72 h-64 bg-gray-100 rounded-xl animate-pulse" />
          ))}
        </div>
      </section>
    );
  }

  if (recommendedDeals.length === 0) {
    return null; // Non mostrare nulla se non ci sono consigli
  }

  return (
    <section className="mt-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-bold text-foreground">Per Te</h2>
          <Sparkles className="h-5 w-5 text-yellow-500" />
        </div>
        <button
          onClick={() => navigate('/deals')}
          className="text-sm text-primary hover:underline"
          aria-label="Vedi tutte le offerte consigliate"
        >
          Vedi tutto
        </button>
      </div>
      
      
      <div ref={dragRef} className="flex space-x-4 overflow-x-auto pb-4 hide-scrollbar cursor-grab active:cursor-grabbing select-none">
        {recommendedDeals.map((deal) => (
          <div key={deal.id} className="flex-shrink-0 w-72">
            <QuickDealCard {...deal} onClick={() => navigate(`/deal/${deal.id}`)} />
          </div>
        ))}
      </div>


    </section>
  );
};

export default PersonalizedRecommendations;
