import { useEffect, useMemo, useState } from "react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import BookingsList from "@/components/bookings/BookingsList";
import { useBooking } from "@/hooks/booking/useBooking";
import { CalendarDays, RefreshCw } from "lucide-react";

export default function CalendarPage() {
  const [date, setDate] = useState<Date>(new Date());
  const { bookings, isLoading } = useBooking();

  // SEO basics without extra deps
  useEffect(() => {
    const title = "Calendario prenotazioni | CatchUp";
    const desc = "Calendario delle prenotazioni: visualizza e gestisci le tue prenotazioni per giorno.";
    document.title = title;

    const ensureMeta = (name: string, content: string, attr: "name" | "property" = "name") => {
      let tag = document.querySelector(`meta[${attr}='${name}']`) as HTMLMetaElement | null;
      if (!tag) {
        tag = document.createElement("meta");
        tag.setAttribute(attr, name);
        document.head.appendChild(tag);
      }
      tag.setAttribute("content", content);
    };

    ensureMeta("description", desc);
    ensureMeta("og:title", title, "property");
    ensureMeta("og:description", desc, "property");

    // canonical
    const href = window.location.origin + "/calendar";
    let link = document.querySelector("link[rel='canonical']") as HTMLLinkElement | null;
    if (!link) {
      link = document.createElement("link");
      link.setAttribute("rel", "canonical");
      document.head.appendChild(link);
    }
    link.setAttribute("href", href);
  }, []);

  const selectedKey = useMemo(() => format(date, "yyyy-MM-dd"), [date]);
  const dayLabel = useMemo(
    () => format(date, "EEEE d MMMM yyyy", { locale: it }),
    [date]
  );

  const bookingsOfDay = useMemo(() => {
    return bookings.filter((b) => {
      try {
        const d = new Date(b.booking_date);
        return format(d, "yyyy-MM-dd") === selectedKey;
      } catch {
        return false;
      }
    });
  }, [bookings, selectedKey]);

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      <header>
        <h1 className="text-3xl font-bold text-foreground">Calendario</h1>
        <p className="text-muted-foreground mt-1">
          Seleziona una data per vedere le tue prenotazioni.
        </p>
      </header>

      <section className="grid md:grid-cols-2 gap-6 items-start">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <CalendarDays className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold text-foreground">Scegli la data</h2>
            </div>
            <Calendar
              mode="single"
              selected={date}
              onSelect={(d) => d && setDate(d)}
              className="p-3 pointer-events-auto"
              modifiersClassNames={{
                selected: "bg-primary text-primary-foreground",
              }}
              initialFocus
            />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-foreground">Le mie prenotazioni</h2>
                <p className="text-sm text-muted-foreground">{dayLabel}</p>
              </div>
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />Aggiorna
              </Button>
            </div>
            <Separator />
            {isLoading ? (
              <div className="py-10 text-center text-muted-foreground">Caricamento...</div>
            ) : (
              <BookingsList bookings={bookingsOfDay as any} />
            )}
          </CardContent>
        </Card>
      </section>

      <article className="sr-only">
        <h2>Calendario delle prenotazioni CatchUp</h2>
        <p>Visualizza le prenotazioni in base alla data selezionata nel calendario.</p>
      </article>
    </div>
  );
}
