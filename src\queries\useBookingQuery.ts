
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { Booking } from '@/types/booking';

// Query keys for better cache management
export const bookingQueryKeys = {
  bookings: {
    all: ['bookings'] as const,
    byUser: (userId: string) => [...bookingQueryKeys.bookings.all, 'user', userId] as const,
  },
};

// Enhanced booking query with proper joins and filtering
export const useGetBookings = (userId: string | undefined, options = {}) => {
  return useQuery({
    queryKey: bookingQueryKeys.bookings.byUser(userId || ''),
    queryFn: async (): Promise<Booking[]> => {
      if (!userId) return [];

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          deals (
            title,
            images,
            business_id,
            businesses (
              name,
              address
            )
          )
        `)
        .eq('user_id', userId)
        .order('booking_date', { ascending: false })
        .order('booking_time', { ascending: false });

      if (error) {
        throw new Error(`Errore nel recupero delle prenotazioni: ${error.message}`);
      }

      return data as Booking[] || [];
    },
    enabled: !!userId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    ...options
  });
};

// Legacy hook for backward compatibility
export const useGetBooking = useGetBookings;
