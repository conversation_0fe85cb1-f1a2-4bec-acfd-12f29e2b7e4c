import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { ArrowLeft, Filter } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { motion } from "framer-motion";
import { toast } from "sonner";
import { useBusinessDetailsQuery, useBusinessDealsQuery } from '@/queries/useBusinessDealsQuery';

import { useAuth } from "@/hooks/auth/useAuth";
import { Skeleton } from "@/components/ui/skeleton";
import { Deal, DealStatus } from "@/types/deals";
import DealCard from "@/components/deals/core/DealCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { delay } from "@/utils/delay";

/**
 * BusinessDeals - Page component that displays all deals for a specific business
 *
 * Displays a header with back navigation and a list of deals from a specific business
 *
 * @example
 * <BusinessDeals />
 */



interface BusinessDetails {
  id: string;
  name: string;
  address: string;
  description?: string;
  website?: string;
  phone?: string;
}

const BusinessDeals = () => {
  const { id } = useParams<{ id: string }>();
  const businessId = id;
  const navigate = useNavigate();
  const { user, userDetails } = useAuth();

  const [deals, setDeals] = useState<Deal[]>([]);
  const [favoriteDeals, setFavoriteDeals] = useState<string[]>([]);
  const [isBusinessOwner, setIsBusinessOwner] = useState(false);
  const [statusFilter, setStatusFilter] = useState<DealStatus | 'all'>('published');
  const [allDeals, setAllDeals] = useState<Deal[]>([]);

  // New state variables for delete confirmation
  const [dealToDelete, setDealToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const { data: business, isLoading: businessLoading, error: businessError } = useBusinessDetailsQuery(businessId);
  const { data: dealsData, isLoading: dealsLoading, error: dealsError, refetch: refetchDeals } = useBusinessDealsQuery(businessId);
  
  const loading = businessLoading || dealsLoading;

  // Check if current user is the business owner
  useEffect(() => {
    if (user && business && business.owner_id === user.id) {
      setIsBusinessOwner(true);
    }
  }, [user, business]);

  // Update deals when data changes
  useEffect(() => {
    if (dealsData) {
      setAllDeals(dealsData as Deal[]);
      
      // Filter deals based on status filter
      if (statusFilter === 'all') {
        setDeals(dealsData as Deal[]);
      } else {
        setDeals((dealsData as Deal[]).filter(deal => deal.status === statusFilter));
      }
    }
  }, [dealsData, statusFilter]);

  useEffect(() => {
    if (businessError) {
      toast.error("Errore nel caricamento dei dettagli dell'attività");
    }
    if (dealsError) {
      toast.error("Errore nel caricamento delle offerte");
    }
  }, [businessError, dealsError]);

  // Get user's favorite deals
  useEffect(() => {
    if (userDetails?.favorite_deals) {
      setFavoriteDeals(userDetails.favorite_deals as string[]);
    }
  }, [userDetails]);

  // Filter deals when status filter changes
  useEffect(() => {
    if (allDeals.length > 0) {
      if (statusFilter === 'all') {
        setDeals(allDeals);
      } else {
        setDeals(allDeals.filter(deal => deal.status === statusFilter));
      }
    }
  }, [statusFilter, allDeals]);

  // Toggle favorite deal status
  const handleToggleFavorite = async (dealId: string) => {
    if (!user) {
      toast.info("Accedi per salvare le offerte nei preferiti");
      return;
    }

    try {
      const isFavorite = favoriteDeals.includes(dealId);
      const newFavorites = isFavorite
        ? favoriteDeals.filter(id => id !== dealId)
        : [...favoriteDeals, dealId];

      setFavoriteDeals(newFavorites);

      const { error } = await supabase
        .from('user_details')
        .update({ favorite_deals: newFavorites })
        .eq('id', user.id);

      if (error) throw error;
    } catch (error) {
      console.error("Error updating favorites:", error);
      toast.error("Errore durante l'aggiornamento dei preferiti");
    }
  };

  // Handle deal deletion
  const handleDeleteDeal = async () => {
    if (!dealToDelete) return;

    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('deals')
        .delete()
        .eq('id', dealToDelete);

      if (error) throw error;

      // Update both deal lists
      const updatedAllDeals = allDeals.filter(deal => deal.id !== dealToDelete);
      setAllDeals(updatedAllDeals);

      // Apply current filter
      if (statusFilter === 'all') {
        setDeals(updatedAllDeals);
      } else {
        setDeals(updatedAllDeals.filter(deal => deal.status === statusFilter));
      }
    //  toast.success("Offerta eliminata con successo");
    } catch (error) {
      console.error("Error deleting deal:", error);
      toast.error("Errore durante l'eliminazione dell'offerta");
    } finally {
      setIsDeleting(false);
      setDealToDelete(null);
    }
  };

  // Handle deal duplication
  const handleDuplicateDeal = async (dealId: string) => {
    try {
      // Find the deal to duplicate
      const dealToDuplicate = deals.find(deal => deal.id === dealId);
      if (!dealToDuplicate) return;

      // Create a copy of the deal with a new title
      const { error } = await supabase
        .from('deals')
        .insert({
          business_id: dealToDuplicate.business_id,
          title: `${dealToDuplicate.title} (copia)`,
          description: dealToDuplicate.description,
          original_price: dealToDuplicate.original_price,
          discount_percentage: dealToDuplicate.discount_percentage,
          discounted_price: dealToDuplicate.discounted_price,
          start_date: dealToDuplicate.start_date,
          end_date: dealToDuplicate.end_date,
          time_slots: dealToDuplicate.time_slots,
          images: dealToDuplicate.images,
          status: dealToDuplicate.status,
          auto_confirm: dealToDuplicate.auto_confirm,
          category_id: dealToDuplicate.category_id,
          terms_conditions: dealToDuplicate.terms_conditions
        });

      if (error) throw error;

   //   toast.success("Offerta duplicata con successo");

      // Refresh the deals list using React Query
      await refetchDeals();

    } catch (error) {
      console.error("Error duplicating deal:", error);
      toast.error("Errore durante la duplicazione dell'offerta");
    }
  };

  return (
    <div className="pb-20 max-w-md mx-auto">
      {/* Header */}
      <header className="fixed top-0 left-0 right-0 bg-white border-b border-gray-100 max-w-md mx-auto z-50">
        <div className="flex items-center px-4 h-16">
          <button
            onClick={() => navigate(-1)}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <h1 className="ml-3 text-xl font-semibold text-gray-800">
            {business?.name || "Tutte le offerte"}
          </h1>
        </div>
      </header>

      {/* Main content */}
      <div className="pt-20 px-4">
        {/* Status filter */}
        {isBusinessOwner && (
          <div className="mb-4">
            <Select
              value={statusFilter}
              onValueChange={(value) => setStatusFilter(value as DealStatus | 'all')}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <SelectValue placeholder="Filtra per stato" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tutte le offerte</SelectItem>
                <SelectItem value="published">Pubblicate</SelectItem>
                <SelectItem value="draft">Bozze</SelectItem>
                <SelectItem value="expired">Scadute</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Business info card */}
        {business && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm p-4 mb-6"
          >
            <h2 className="text-lg font-bold text-gray-800">{business.name}</h2>
            {business.address && (
              <p className="text-gray-600 text-sm mt-1">{business.address}</p>
            )}
            {business.description && (
              <p className="text-gray-700 mt-3">{business.description}</p>
            )}
            {(business.phone || business.website) && (
              <div className="mt-4 space-y-2">
                {business.phone && (
                  <a
                    href={`tel:${business.phone}`}
                    className="block text-brand-primary font-medium"
                  >
                    {business.phone}
                  </a>
                )}
                {business.website && (
                  <a
                    href={business.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-brand-primary font-medium"
                  >
                    Visita il sito web
                  </a>
                )}
              </div>
            )}
          </motion.div>
        )}

        {/* Deals list */}
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i}>
                <div className="bg-gray-200 h-48 rounded-t-xl w-full"></div>
                <div className="bg-white rounded-b-xl p-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                </div>
              </Skeleton>
            ))}
          </div>
        ) : deals.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500">Nessuna offerta disponibile al momento</p>
          </div>
        ) : (
          <div className="space-y-4">
            {deals.map(deal => (
              <DealCard
                key={deal.id}
                deal={deal}
                variant="full"
                isFavorite={favoriteDeals.includes(deal.id)}
                onFavoriteClick={() => handleToggleFavorite(deal.id)}
                onClick={() => navigate(`/deal/${deal.id}`)}
                onEditClick={() => navigate(`/deal/${deal.id}/edit`)}
                onDeleteClick={() => setDealToDelete(deal.id)}
                onDuplicateClick={() => handleDuplicateDeal(deal.id)}
                isBusinessOwner={isBusinessOwner}
                showVisitBusiness={false}
              />
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!dealToDelete} onOpenChange={(open) => !open && setDealToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Conferma eliminazione</AlertDialogTitle>
            <AlertDialogDescription>
              Sei sicuro di voler eliminare questa offerta? Questa azione non può essere annullata.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Annulla</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDeal}
              disabled={isDeleting}

            >
              {isDeleting ? "Eliminazione..." : "Elimina"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default BusinessDeals;
