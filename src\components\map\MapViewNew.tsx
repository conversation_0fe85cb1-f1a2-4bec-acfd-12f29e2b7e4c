import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import useSupercluster from "use-supercluster";
import { APIProvider, Map, AdvancedMarker, useMap } from "@vis.gl/react-google-maps";
import { Store, X, MapPin, Navigation2, ChevronRight, Clock, Car, PersonStanding, Bus, Bike, User2 } from "lucide-react";
import { Carousel, CarouselContent, CarouselItem } from "@/components/ui/carousel";
import toastNotification from "@/lib/toast";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";
import { debounce } from "lodash";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { MAPVIEW_DEFAULT_ZOOM, NEARBY_DEFAULT_RADIUS_IN_METERS } from "@/data/userSettings";
import DealCard from "../deals/core/DealCard";
import { useDealsByBusinessIdQuery } from "@/queries/useDealsQuery";
import Hint from "../Hint";
import { SelectedLocationMarker } from "./SelectedLocationMarker";
interface Business {
  id: string;
  name: string;
  latitude: number | null;
  longitude: number | null;
  deal_count_published: number | null;
  deals?: any[];
  fake: boolean;
  icon?: string | null;
}
interface MapViewNewProps {
  businesses: Business[];
  onBusinessClick?: (business: Business) => void;
  onDealClick?: (dealId: string) => void;
  locationEnabled?: boolean;
  userCircleRadius: number;
  selectedLocation?: { lat: number; lng: number };
  selectedLocationName?: string;
  onLocationChange?: (location: string) => void;
}
type TransportMode = "DRIVING" | "WALKING" | "TRANSIT" | "BICYCLING";
interface NavigationState {
  isActive: boolean;
  mode: TransportMode;
  directions: google.maps.DirectionsResult | null;
}



// Custom marker component for businesses
const BusinessMarker = ({
  business,
  onClick
}: {
  business: Business;
  onClick: () => void;
}) => {
  const getBusinessIconSvg = (iconName: string | null) => {
    if (!iconName) {
      return `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#4F46E5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7"/>
        <path d="M4 7v6a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7"/>
        <path d="M8 13v4"/>
        <path d="M16 13v4"/>
      </svg>`;
    }
    const iconMappings: {
      [key: string]: string;
    } = {
      "openmoji:shopping-cart": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
      </svg>`,
      "openmoji:person-lifting-weights": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L4.86 7.71l8.58 8.58-1.43 1.43L13.43 19l1.43 1.43L16.29 19l1.43 1.43 1.43-1.43-1.43-1.43L19.14 16l1.43-1.14zM12.8 12.8c-.78.78-2.05.78-2.83 0-.78-.78-.78-2.05 0-2.83.78-.78 2.05-.78 2.83 0 .78.78.78 2.04 0 2.83z"/>
      </svg>`,
      "openmoji:fork-and-knife": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.20-1.10-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41L13.41 13l1.47-1.47z"/>
      </svg>`,
      "openmoji:beer-mug": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M5 6V4h5.5l1.21-1.21C12.45 2.04 13.18 2 14 2s1.55.04 2.29.79L17.5 4H20v2h-1v11c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2V6H5zm2 11h8V6H7v11zm2-9h1v7H9V8zm3 0h1v7h-1V8z"/>
      </svg>`,
      "openmoji:hotel": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H9.5v-7h-8v12H3v-2h18v2h1.5V7z"/>
      </svg>`,
      "openmoji:scissors": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM19 3l-6 6 2 2 7-7V3z"/>
      </svg>`,
      "openmoji:person-in-lotus-position": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M8.55 12C9.66 7.7 12.14 5.7 12.14 5.7S14.62 7.7 15.73 12C16.84 16.3 12.14 18.3 12.14 18.3S7.44 16.3 8.55 12ZM12.14 2C12.14 2 6.34 5.56 5.78 12.95C5.18 21.03 11.88 22 12.14 22S19.1 21.03 18.5 12.95C17.94 5.56 12.14 2 12.14 2Z"/>
      </svg>`,
      "openmoji:bouquet": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M12 22c4.97 0 9-4.03 9-9-4.97 0-9 4.03-9 9zM5.6 10.25c0 1.38 1.12 2.5 2.5 2.5.53 0 1.01-.16 1.42-.44l-.02.19c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5l-.02-.19c.4.28.89.44 1.42.44 1.38 0 2.5-1.12 2.5-2.5 0-1.25-.93-2.29-2.14-2.46.21-.34.34-.74.34-1.18 0-1.25-.93-2.29-2.14-2.46C14.48 3.94 13.75 3.5 12.9 3.5c-.85 0-1.58.44-2.02.85C9.67 4.21 8.74 5.25 8.74 6.5c0 .44.13.84.34 1.18C7.87 7.85 6.94 8.89 6.94 10.14c0 .44.13.84.34 1.18C6.07 11.49 5.6 10.92 5.6 10.25z"/>
      </svg>`,
      "openmoji:nail-polish": `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
        <path d="M8.6 10.08c1.78 0 3.22-1.44 3.22-3.22S10.38 3.64 8.6 3.64 5.38 5.08 5.38 6.86s1.44 3.22 3.22 3.22zm6.8 0c1.78 0 3.22-1.44 3.22-3.22S17.18 3.64 15.4 3.64s-3.22 1.44-3.22 3.22 1.44 3.22 3.22 3.22zM8.6 12.5c-2.33 0-7 1.17-7 3.5V18c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-2c0-2.33-4.67-3.5-7-3.5zm6.8 0c-.29 0-.62.02-.97.05.02.01.03.03.04.04 1.14.83 1.93 1.94 1.93 3.41V18c0 .35-.07.69-.18 1H22c.55 0 1-.45 1-1v-2c0-2.33-4.67-3.5-7-3.5z"/>
      </svg>`
    };
    return iconMappings[iconName] || `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="#4F46E5">
      <circle cx="12" cy="12" r="10"/>
      <path d="M12 6v6l4 2"/>
    </svg>`;
  };
  const createMarkerContent = () => {
    return `
      <div style="
        background: white;
        border: 1.5px solid #A0AEC0;
        border-radius: 12px;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        gap: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        font-family: system-ui, -apple-system, sans-serif;
        position: relative;
        min-width: 120px;
        max-width: 200px;
      ">
        <div style="flex-shrink: 0;">
          ${getBusinessIconSvg(business.icon)}
        </div>
        <div style="flex: 1; min-width: 0;">
          <div style="
            font-weight: 600;
            color: #1F2937;
            font-size: 11px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
          ">
            ${business.name}
          </div>
          ${business.deal_count_published && business.deal_count_published > 0 ? `
            <div style="
              color: #f44336;
              font-size: 10px;
              font-weight: 500;
              margin-top: 2px;
              display: flex;
              align-items: center;
              line-height: 1.2;
            ">
              <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 2px;">
                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                <line x1="7" y1="7" x2="7.01" y2="7"></line>
              </svg>
              ${business.deal_count_published} ${business.deal_count_published > 1 ? "Offerte" : "Offerta"}
            </div>
          ` : ""}
        </div>
        <div style="
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid #A0AEC0;
        "></div>
        <div style="
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 5px solid transparent;
          border-right: 5px solid transparent;
          border-top: 5px solid white;
        "></div>
      </div>
    `;
  };
  if (!business.latitude || !business.longitude) return null;
  return <AdvancedMarker position={{
    lat: business.latitude,
    lng: business.longitude
  }} onClick={onClick}>
      <div dangerouslySetInnerHTML={{
      __html: createMarkerContent()
    }} style={{
      cursor: "pointer"
    }} />
    </AdvancedMarker>;
};

// Cluster marker component for grouped businesses
const ClusterMarker = ({
  cluster,
  onClick
}: {
  cluster: any;
  onClick: () => void;
}) => {
  const [longitude, latitude] = cluster.geometry.coordinates;
  const {
    point_count: pointCount
  } = cluster.properties;
  const size = Math.min(40 + pointCount / 100 * 20, 80); // Scale size based on point count

  return <AdvancedMarker position={{
    lat: latitude,
    lng: longitude
  }} onClick={onClick}>
      <div style={{
      width: `${size}px`,
      height: `${size}px`,
      backgroundColor: "#4F46E5",
      borderRadius: "50%",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "white",
      fontWeight: "bold",
      fontSize: `${Math.min(size / 4, 14)}px`,
      cursor: "pointer",
      border: "3px solid white",
      boxShadow: "0 2px 8px rgba(0,0,0,0.3)"
    }}>
        {pointCount}
      </div>
    </AdvancedMarker>;
};

// User location marker component
const UserLocationMarker = ({
  position
}: {
  position: {
    lat: number;
    lng: number;
  };
}) => {
  const createUserMarkerContent = () => {
    return `
      <div style="
        position: relative;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      ">
        <div style="
          width: 20px;
          height: 20px;
          background: #4F46E5;
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 8px rgba(0,0,0,0.3);
          position: relative;
          z-index: 10;
        "/>
        <div style="
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 40px;
          height: 40px;
          background: rgba(79, 70, 229, 0.2);
          border-radius: 50%;
          animation: cu-map-pulse 2s ease-in-out infinite;
          z-index: 10;
        "/>
      </div>
      <style>
        @keyframes cu-map-pulse {
          0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
          100% { transform: translate(-50%, -50%) scale(2); opacity: 0; }
        }
      </style>
    `;
  };
  return <AdvancedMarker position={position}>
      <div dangerouslySetInnerHTML={{
      __html: createUserMarkerContent()
    }} style={{
      cursor: "pointer"
    }} />
    </AdvancedMarker>;
};

// Circle component for user radius
const UserCircle = ({
  center,
  radius,
}: //onRadiusChange,

{
  center: { lat: number; lng: number };
  radius: number;
  //onRadiusChange: (radius: number) => void;
}) => {
  const map = useMap();
  const [circle, setCircle] = useState<google.maps.Circle | null>(null);
  useEffect(() => {
    if (!map) return;
    const newCircle = new google.maps.Circle({
      center,
      radius,
      strokeColor: "#4F46E5",
      strokeWeight: 2,
      strokeOpacity: 0.5,
      fillColor: "#4F46E5",
      fillOpacity: 0.1,
      map
    });
    setCircle(newCircle);
    return () => {
      newCircle.setMap(null);
    };
  }, [map, center]);
  useEffect(() => {
    if (circle) {
      circle.setRadius(radius);
    }
  }, [circle, radius]);
  return null;
};

// Directions renderer component
const DirectionsRenderer = ({
  directions
}: {
  directions: google.maps.DirectionsResult | null;
}) => {
  const map = useMap();
  const [directionsRenderer, setDirectionsRenderer] = useState<google.maps.DirectionsRenderer | null>(null);
  useEffect(() => {
    if (!map) return;
    const renderer = new google.maps.DirectionsRenderer({
      suppressMarkers: false,
      polylineOptions: {
        strokeColor: "#4F46E5",
        strokeWeight: 6,
        strokeOpacity: 0.8
      },
      markerOptions: {
        zIndex: 2000
      },
      preserveViewport: false
    });
    renderer.setMap(map);
    setDirectionsRenderer(renderer);
    return () => {
      renderer.setMap(null);
    };
  }, [map]);
  useEffect(() => {
    if (directionsRenderer && directions) {
      directionsRenderer.setDirections(directions);
    } else if (directionsRenderer) {
      directionsRenderer.setDirections(null);
    }
  }, [directionsRenderer, directions]);
  return null;
};

// Main map component that uses the map instance
const RenderMapContent = ({
  businesses,
  onBusinessClick,
  onDealClick,
  locationEnabled = true,
  userCircleRadius,
  selectedLocation,
  selectedLocationName,
  onLocationChange
}: MapViewNewProps) => {
  
  const map = useMap();
  const [selectedBusiness, setSelectedBusiness] = useState<Business | null>(null);
  const [isSlideVisible, setIsSlideVisible] = useState(false);
  const slideRef = useRef<HTMLDivElement>(null);
  const touchStartY = useRef<number | null>(null);
  const [currentBounds, setCurrentBounds] = useState<google.maps.LatLngBounds | null>(null);
  const [isLoadingMarkers, setIsLoadingMarkers] = useState(false);
  const [navigationState, setNavigationState] = useState<NavigationState>({
    isActive: false,
    mode: "DRIVING",
    directions: null
  });
  const [showTransportMenu, setShowTransportMenu] = useState(false);

  const { userLocation } = useLocationManagement();
  // console.log(
  //   "User Location",
  //   userLocation,
  //   "locationEnabled",
  //   locationEnabled
  // );
  //const [userCircleRadius, setUserCircleRadius] =  useState<number>(NEARBY_DEFAULT_RADIUS_IN_METERS);

  // Center map on user location when component loads
  useEffect(() => {
    if (map && userLocation && locationEnabled) {
      map.panTo(userLocation);
    }
  }, [map, userLocation, locationEnabled]);
  // Clustering state
  const [mapBounds, setMapBounds] = useState<
    [number, number, number, number] | null
  >(null);
  const [mapZoom, setMapZoom] = useState<number>(MAPVIEW_DEFAULT_ZOOM);

  // Convert businesses to GeoJSON format for clustering
  const points = useMemo(() => {
    return businesses.filter(business => business.latitude && business.longitude).map(business => ({
      type: "Feature" as const,
      properties: {
        cluster: false,
        businessId: business.id,
        business: business
      },
      geometry: {
        type: "Point" as const,
        coordinates: [business.longitude!, business.latitude!]
      }
    }));
  }, [businesses]);

  // Get clusters using supercluster
  const {
    clusters,
    supercluster
  } = useSupercluster({
    points,
    bounds: mapBounds,
    zoom: mapZoom,
    options: {
      radius: 75,
      maxZoom: 17
    }
  });

  // const increaseRadius = useCallback(() => {
  //   setUserCircleRadius((prev) => prev + 100);
  // }, []);

  // const decreaseRadius = useCallback(() => {
  //   setUserCircleRadius((prev) => Math.max(100, prev - 100));
  // }, []);

  const handleBoundsChanged = useCallback(
    debounce(() => {
      if (map) {
        setIsLoadingMarkers(true);
        const bounds = map.getBounds();
        const zoom = map.getZoom();

        if (bounds) {
          setCurrentBounds(bounds);
          // Set clustering bounds in [west, south, east, north] format
          setMapBounds([
            bounds.toJSON().west,
            bounds.toJSON().south,
            bounds.toJSON().east,
            bounds.toJSON().north,
          ]);
        }

        if (zoom !== undefined) {
          setMapZoom(zoom);
        }

        setTimeout(() => {
          setIsLoadingMarkers(false);
        }, 500);
      }
    }, 300),
    [map]
  );

  // Handle cluster click to zoom into cluster
  const handleClusterClick = useCallback((cluster: any) => {
    if (!map || !supercluster) return;
    const [longitude, latitude] = cluster.geometry.coordinates;
    const expansionZoom = Math.min(supercluster.getClusterExpansionZoom(cluster.id), 17);
    map.setZoom(expansionZoom);
    map.panTo({
      lat: latitude,
      lng: longitude
    });
  }, [map, supercluster]);

  // Note: visibleBusinesses logic removed since we're using clustering

  const handleMarkerClick = async (business: Business) => {
    if (selectedBusiness?.id !== business.id) {
      setNavigationState({
        isActive: false,
        mode: "DRIVING",
        directions: null
      });
      setShowTransportMenu(false);
    }
    
    if (!business.deals || business.deals.length === 0) {
      try {
        const { data, error } = await supabase
          .from("deals")
          .select("*, businesses(*)")
          .eq("business_id", business.id)
          .eq("status", "published");
        
        if (error) {
          throw error;
        }
        
        business.deals = data || [];
      } catch (error) {
        console.error("Errore nel recupero delle offerte:", error);
        toastNotification.error("Errore nel recupero delle offerte");
      }
    }
    
    setSelectedBusiness({
      ...business
    });
    setIsSlideVisible(true);
    if (onBusinessClick) {
      onBusinessClick(business);
    }
  };
  const calculateRoute = useCallback(async (destination: Business, mode: TransportMode) => {
    if (!userLocation || !destination.latitude || !destination.longitude) {
      toastNotification.error("Impossibile calcolare il percorso. Assicurati di aver attivato la geolocalizzazione.");
      return;
    }
    const directionsService = new google.maps.DirectionsService();
    try {
      const result = await directionsService.route({
        origin: userLocation,
        destination: {
          lat: destination.latitude,
          lng: destination.longitude
        },
        travelMode: google.maps.TravelMode[mode],
        language: "it"
      });
      setNavigationState({
        isActive: true,
        mode,
        directions: result
      });
      if (map && result.routes[0].bounds) {
        map.fitBounds(result.routes[0].bounds);
      }
    } catch (error) {
      console.error("Error calculating route:", error);
      toastNotification.error("Errore nel calcolo del percorso");
    }
  }, [userLocation, map]);
  const clearNavigation = () => {
    setNavigationState({
      isActive: false,
      mode: "DRIVING",
      directions: null
    });
    setShowTransportMenu(false);
  };
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor(seconds % 3600 / 60);
    if (hours > 0) {
      return `${hours} h ${minutes} min`;
    }
    return `${minutes} min`;
  };
  const formatDistance = (meters: number): string => {
    if (meters >= 1000) {
      return `${(meters / 1000).toFixed(1)} km`;
    }
    return `${meters} m`;
  };
  const getTransportIcon = (mode: TransportMode) => {
    switch (mode) {
      case "DRIVING":
        return <Car className="h-4 w-4" />;
      case "WALKING":
        return <PersonStanding className="h-4 w-4" />;
      case "TRANSIT":
        return <Bus className="h-4 w-4" />;
      case "BICYCLING":
        return <Bike className="h-4 w-4" />;
      default:
        return null;
    }
  };
  const centerMapToUserLocation = useCallback(() => {
    if (userLocation && map) {
      map.panTo(userLocation);
      map.setZoom(MAPVIEW_DEFAULT_ZOOM);
      toastNotification.success("Mappa centrata sulla tua posizione");
      
      // Reset the location in the toolbar to default
      onLocationChange?.("Posizione Attuale");
    } else if (!userLocation) {
      toastNotification.error("Posizione utente non disponibile");
    }
  }, [userLocation, map, onLocationChange]);
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartY.current = e.touches[0].clientY;
  };
  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;
    const touchY = e.touches[0].clientY;
    const diff = touchY - touchStartY.current;
    if (diff > 0) {
      slideRef.current.style.transform = `translateY(${diff}px)`;
    }
  };
  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartY.current === null || !slideRef.current) return;
    const touchY = e.changedTouches[0].clientY;
    const diff = touchY - touchStartY.current;
    slideRef.current.style.transform = "";
    if (diff > 100) {
      setIsSlideVisible(false);
      setTimeout(() => setSelectedBusiness(null), 300);
    }
    touchStartY.current = null;
  };

  // Move map to selected location when it changes
  useEffect(() => {
    if (selectedLocation && map) {
      console.log("Spostando la mappa alla posizione selezionata:", selectedLocation, selectedLocationName);
      map.panTo(selectedLocation);
      map.setZoom(15); // Zoom in per mostrare meglio la nuova area
    }
  }, [selectedLocation, map, selectedLocationName]);

  // Set up map event listeners and initial bounds
  useEffect(() => {
    if (map) {
      const boundsListener = map.addListener("bounds_changed", handleBoundsChanged);

      // Set initial bounds and zoom for clustering
      const initialBounds = map.getBounds();
      const initialZoom = map.getZoom();
      if (initialBounds) {
        setMapBounds([initialBounds.toJSON().west, initialBounds.toJSON().south, initialBounds.toJSON().east, initialBounds.toJSON().north]);
      }
      if (initialZoom !== undefined) {
        setMapZoom(initialZoom);
      }
      return () => {
        google.maps.event.removeListener(boundsListener);
      };
    }
  }, [map, handleBoundsChanged]);
  return <>
      {/* Clustered markers */}
      {!navigationState.isActive && clusters.map(cluster => {
      const [longitude, latitude] = cluster.geometry.coordinates;
      const {
        cluster: isCluster,
        business
      } = cluster.properties;
      if (isCluster) {
        return <ClusterMarker key={`cluster-${cluster.id}`} cluster={cluster} onClick={() => handleClusterClick(cluster)} />;
      }
      return <BusinessMarker key={`business-${business.id}`} business={business} onClick={() => handleMarkerClick(business)} />;
    })}

      {/* Position markers and circles - show only one at a time */}
      {(() => {
        // Priority logic: SearchPosition over CurrentPosition
        const hasSearchPosition = selectedLocation && selectedLocationName && selectedLocationName !== "Posizione Attuale";
        
        if (hasSearchPosition) {
          // Show SearchPosition
          return (
            <>
              <SelectedLocationMarker 
                position={selectedLocation!} 
                title={selectedLocationName || "Posizione selezionata"}
              />
              <UserCircle
                center={selectedLocation!}
                radius={userCircleRadius}
              />
            </>
          );
        } else if (userLocation && locationEnabled) {
          // Show CurrentPosition (user's real, demo, or fallback position)
          return (
            <>
              <UserLocationMarker position={userLocation} />
              <UserCircle
                center={userLocation}
                radius={userCircleRadius}
              />
            </>
          );
        }
        
        return null;
      })()}

      {/* Directions */}
      {navigationState.directions && <DirectionsRenderer directions={navigationState.directions} />}

      {/* User location controls */}
      {userLocation && (
        <div className="fixed left-4 bottom-20 z-10 flex flex-col gap-2">
          <Hint text="Centra sulla tua posizione">
            <button
              onClick={centerMapToUserLocation}
              className="bg-white shadow-lg rounded-full p-3 hover:bg-gray-50 transition-colors border border-brand-primary flex items-center justify-center"
              aria-label="Centra sulla tua posizione"
            >
              <User2 className="h-4 w-4 text-brand-primary" />
            </button>
          </Hint>

          {/* Radius control buttons */}
          {/* <div className="bg-white shadow-lg rounded-full flex flex-col border border-brand-primary overflow-hidden">
            <button
              //onClick={increaseRadius}
              className="p-2 hover:bg-gray-50 transition-colors border-b border-gray-200 focus:outline-none flex items-center justify-center"
              title="Aumenta raggio"
              aria-label="Aumenta raggio"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-brand-primary"
              >
                <path d="M12 5v14M5 12h14"></path>
              </svg>
            </button>

      
            <div className="relative group">
              <div className="flex items-center justify-center p-1.5 border-b border-gray-200 bg-gray-50">
                <span className="text-xs font-medium text-brand-primary">
                  {(userCircleRadius / 1000).toFixed(1)} km
                </span>
              </div>
             
              <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity">
                Raggio di ricerca
              </div>
            </div>

            <button
              // onClick={decreaseRadius}
              className="p-2 hover:bg-gray-50 transition-colors focus:outline-none flex items-center justify-center"
              title="Diminuisci raggio"
              aria-label="Diminuisci raggio"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-brand-primary"
              >
                <path d="M5 12h14"></path>
              </svg>
            </button>
          </div> */}
        </div>
      )}

      {/* Loading indicator */}
      {isLoadingMarkers && <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 bg-white/70 px-4 py-2 rounded-full shadow-md z-20 flex items-center">
          <span className="text-sm font-medium mr-2">Caricamento</span>
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-brand-primary rounded-full animate-bounce" style={{
          animationDelay: "0ms"
        }}></div>
            <div className="w-2 h-2 bg-brand-primary rounded-full animate-bounce" style={{
          animationDelay: "150ms"
        }}></div>
            <div className="w-2 h-2 bg-brand-primary rounded-full animate-bounce" style={{
          animationDelay: "300ms"
        }}>
              {" "}
            </div>
          </div>
        </div>}

      {/* Navigation controls */}
      <div className="fixed left-4 top-24 z-50">
        <div className="relative">
          {selectedBusiness ? <button onClick={() => {
          setShowTransportMenu(!showTransportMenu);
        }} className={cn("fixed left-4 top-20 z-10 bg-brand-primary shadow-lg rounded-full px-5 py-2.5", navigationState.directions ? "hover:bg-brand-primary/90 flex items-center gap-2" : "hover:bg-brand-primary/50")} title={navigationState.directions ? "Cambia mezzo di trasporto" : "Naviga verso l'attività"}>
              <Navigation2 className="h-6 w-6 text-white" />
              {navigationState.directions && <span className="text-white text-sm font-medium flex items-center gap-2">
                  {getTransportIcon(navigationState.mode)}
                  {formatDuration(navigationState.directions.routes[0].legs[0].duration.value)}
                </span>}
            </button> : null}

          {showTransportMenu && selectedBusiness && <div className="absolute top-full mb-2 left-0 bg-white rounded-lg shadow-lg p-2 min-w-[200px] z-50">
              <div className="flex flex-col gap-2">
                <div className="px-4 py-2 text-sm font-medium text-gray-600 border-b border-gray-100">
                  {navigationState.directions ? "Opzioni percorso" : "Scegli il mezzo"}
                </div>
                {navigationState.directions ? <>
                    <button onClick={() => {
                setNavigationState(prev => ({
                  ...prev,
                  isActive: true
                }));
                setShowTransportMenu(false);
              }} className="flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md">
                      <ChevronRight className="h-4 w-4" /> Mostra indicazioni
                    </button>
                    <div className="border-t border-gray-100 my-1"></div>
                  </> : null}
                <button onClick={() => {
              calculateRoute(selectedBusiness, "DRIVING");
              setShowTransportMenu(false);
            }} className={cn("flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md", navigationState.mode === "DRIVING" && "text-brand-primary font-medium")}>
                  <Car className="h-4 w-4" /> In auto
                </button>
                <button onClick={() => {
              calculateRoute(selectedBusiness, "WALKING");
              setShowTransportMenu(false);
            }} className={cn("flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md", navigationState.mode === "WALKING" && "text-brand-primary font-medium")}>
                  <PersonStanding className="h-4 w-4" /> A piedi
                </button>
                <button onClick={() => {
              calculateRoute(selectedBusiness, "TRANSIT");
              setShowTransportMenu(false);
            }} className={cn("flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md", navigationState.mode === "TRANSIT" && "text-brand-primary font-medium")}>
                  <Bus className="h-4 w-4" /> Mezzi pubblici
                </button>
                <button onClick={() => {
              calculateRoute(selectedBusiness, "BICYCLING");
              setShowTransportMenu(false);
            }} className={cn("flex items-center gap-2 px-4 py-2 hover:bg-gray-100 rounded-md", navigationState.mode === "BICYCLING" && "text-brand-primary font-medium")}>
                  <Bike className="h-4 w-4" /> In bicicletta
                </button>
              </div>
            </div>}
        </div>
      </div>

      {/* Navigation sidebar */}
      {navigationState.isActive && navigationState.directions && <div className="fixed right-4 top-24 bottom-24 w-80 bg-white rounded-lg shadow-lg z-50 flex flex-col overflow-hidden">
          <div className="p-4 bg-brand-primary text-white">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold">Indicazioni stradali</h3>
              <button onClick={() => {
            setNavigationState(prev => ({
              ...prev,
              isActive: false
            }));
          }} className="p-1 hover:bg-white/10 rounded-full transition-colors">
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <div className="flex items-center gap-1.5">
                {getTransportIcon(navigationState.mode)}
                <Clock className="h-4 w-4" />
              </div>
              <span>
                {formatDuration(navigationState.directions.routes[0].legs[0].duration.value)}
              </span>
              <span className="mx-1">·</span>
              <span>
                {formatDistance(navigationState.directions.routes[0].legs[0].distance.value)}
              </span>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">
              {navigationState.directions.routes[0].legs[0].steps.map((step, index) => <div key={index} className="flex gap-3">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center">
                      {index === 0 ? <div className="w-2 h-2 rounded-full bg-brand-primary" /> : <ChevronRight className="h-4 w-4 text-gray-400" />}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm text-gray-700" dangerouslySetInnerHTML={{
                __html: step.instructions
              }} />
                      <div className="text-xs text-gray-500 mt-1">
                        {formatDistance(step.distance.value)} ·{" "}
                        {formatDuration(step.duration.value)}
                      </div>
                    </div>
                  </div>)}
            </div>
          </div>
        </div>}

      {/* Business details slide panel */}
      {selectedBusiness && <div ref={slideRef} className={cn("fixed left-0 right-0 z-10 transition-all duration-300 ease-in-out", isSlideVisible ? "bottom-16 opacity-100" : "-bottom-full opacity-0")} onTouchStart={handleTouchStart} onTouchMove={handleTouchMove} onTouchEnd={handleTouchEnd}>
          <div className="relative bg-white rounded-t-xl shadow-lg p-4 mx-4 max-h-[70vh] overflow-y-auto">
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gray-300 rounded-full mb-4"></div>

            <button onClick={() => {
          setIsSlideVisible(false);
          clearNavigation();
          setTimeout(() => setSelectedBusiness(null), 300);
        }} className="absolute right-2 top-2 p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
              <X className="h-4 w-4 text-gray-600" />
            </button>

            <div className="flex items-center gap-2 mb-4 mt-4">
              <Store className="h-5 w-5 text-brand-primary" />
              <h3 className="font-semibold text-lg">
                {selectedBusiness.name}
                <span className="text-sm text-gray-500">
                  <span className="text-sm text-gray-500 mx-[8px]">
                    {selectedBusiness.deals.length} offerte
                  </span>
                </span>
              </h3>
            </div>

            {selectedBusiness.deals && selectedBusiness.deals.length > 0 ? <Carousel className="w-full">
                <CarouselContent>
                  {selectedBusiness.deals.map(deal => <CarouselItem key={deal.id}>
                      <DealCard deal={deal} variant="full" onClick={() => onDealClick?.(deal.id)} showVisitBusiness={true} />
                    </CarouselItem>)}
                </CarouselContent>
              </Carousel> : <p className="text-gray-500 text-center py-4">
                Nessuna offerta disponibile al momento
              </p>}
          </div>
        </div>}
    </>;
};
export const MapViewNew = (props: MapViewNewProps) => {
  const [apiKey, setApiKey] = useState<string>("");
  const [center, setCenter] = useState<{
    lat: number;
    lng: number;
  }>({
    lat: 45.4671,
    lng: 9.1526
  });
  const {
    userLocation
  } = useLocationManagement();
  useEffect(() => {
    if (userLocation && props.locationEnabled) {
      setCenter(userLocation);
    }
  }, [userLocation, props.locationEnabled]);
  useEffect(() => {
    const getGoogleMapsKey = async () => {
      const {
        data: {
          GOOGLE_MAPS_API_KEY
        },
        error
      } = await supabase.functions.invoke("get-google-maps-key");
      if (error) {
        console.error("Errore nel recupero della chiave API:", error);
        return;
      }
      setApiKey(GOOGLE_MAPS_API_KEY);
    };
    getGoogleMapsKey();
  }, []);
  if (!apiKey) {
    return <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center h-[calc(100vh-120px)]">
          <div className="animate-spin">
            <MapPin className="h-8 w-8 text-brand-primary" />
          </div>
        </div>
      </div>;
  }
  return <div className="relative w-full h-full">
      <APIProvider apiKey={apiKey}>
        
        <Map
          style={{ width: "100%", height: "100%" }}
          defaultCenter={center}
          defaultZoom={MAPVIEW_DEFAULT_ZOOM}
          gestureHandling="greedy"
          disableDefaultUI={true}
          mapTypeControl={false}
          streetViewControl={false}
          fullscreenControl={false}
          mapId="catchup-map" // Required for AdvancedMarker
        >
          <RenderMapContent {...props} />
        </Map>
      </APIProvider>
    </div>;
};
