import { useEffect, useRef } from "react";

// Abilita drag-to-scroll orizzontale su container con overflow-x
export function useDragScroll<T extends HTMLElement>() {
  const ref = useRef<T | null>(null);

  useEffect(() => {
    const el = ref.current;
    if (!el) return;

    let isDown = false;
    let startX = 0;
    let scrollLeft = 0;
    let moved = false;

    const onMouseDown = (e: MouseEvent) => {
      isDown = true;
      moved = false;
      startX = e.pageX - el.offsetLeft;
      scrollLeft = el.scrollLeft;
    };

    const onMouseLeave = () => {
      isDown = false;
    };

    const onMouseUp = () => {
      isDown = false;
      // Pi<PERSON><PERSON> timeout per evitare click fantasma subito dopo il drag
      setTimeout(() => (moved = false), 0);
    };

    const onMouseMove = (e: MouseEvent) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.pageX - el.offsetLeft;
      const walk = x - startX;
      if (Math.abs(walk) > 3) moved = true;
      el.scrollLeft = scrollLeft - walk;
    };

    const onClick = (e: MouseEvent) => {
      if (moved) {
        // Se abbiamo trascinato, previeni il click su pulsanti/link all'interno
        e.preventDefault();
        e.stopPropagation();
      }
    };

    // Touch support
    let tStartX = 0;
    let tScrollLeft = 0;
    const onTouchStart = (e: TouchEvent) => {
      isDown = true;
      moved = false;
      tStartX = e.touches[0].pageX;
      tScrollLeft = el.scrollLeft;
    };
    const onTouchMove = (e: TouchEvent) => {
      if (!isDown) return;
      const x = e.touches[0].pageX;
      const walk = x - tStartX;
      if (Math.abs(walk) > 3) moved = true;
      el.scrollLeft = tScrollLeft - walk;
    };
    const onTouchEnd = () => {
      isDown = false;
      setTimeout(() => (moved = false), 0);
    };

    el.addEventListener("mousedown", onMouseDown);
    el.addEventListener("mouseleave", onMouseLeave);
    el.addEventListener("mouseup", onMouseUp);
    el.addEventListener("mousemove", onMouseMove);
    el.addEventListener("click", onClick, { capture: true });

    el.addEventListener("touchstart", onTouchStart, { passive: true });
    el.addEventListener("touchmove", onTouchMove, { passive: true });
    el.addEventListener("touchend", onTouchEnd);

    return () => {
      el.removeEventListener("mousedown", onMouseDown);
      el.removeEventListener("mouseleave", onMouseLeave);
      el.removeEventListener("mouseup", onMouseUp);
      el.removeEventListener("mousemove", onMouseMove);
      el.removeEventListener("click", onClick, { capture: true } as any);

      el.removeEventListener("touchstart", onTouchStart as any);
      el.removeEventListener("touchmove", onTouchMove as any);
      el.removeEventListener("touchend", onTouchEnd as any);
    };
  }, []);

  return ref;
}
