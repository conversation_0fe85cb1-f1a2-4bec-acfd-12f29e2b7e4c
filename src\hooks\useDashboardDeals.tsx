import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useAuth } from "@/hooks/auth/useAuth";
import { useLocationManagement } from "@/hooks/location/useLocationManagement";
import { Deal } from "@/types/deals";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { NEARBY_DEFAULT_RADIUS_IN_METERS } from "@/data/userSettings";
import type { Database } from "@/integrations/supabase/types";
import { useNearbyDealsSearch } from "@/hooks/search/useNearbyDealsSearch";
import { mapNearbyDealsToDealFormat } from "@/utils/dealMappers";
import { useFeaturedDealsQuery } from "@/queries/useDealsQuery";
import { useRecentlyViewedDealsQuery, useFavoriteDealsQuery } from "@/queries/useDashboardDealsQuery";

// Helper function to calculate current time as percentage of the day
const getCurrentTimePercentage = (): number => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const totalMinutesSinceMidnight = hours * 60 + minutes;
  const totalMinutesInDay = 24 * 60;
  return (totalMinutesSinceMidnight / totalMinutesInDay) * 100;
};

export const useDashboardDeals = () => {
  const { user } = useAuth();
  const { userLocation } = useLocationManagement();

  // Time and date selection state
  const [selectedDate, setSelectedDate] = useState<string>(
    format(new Date(), "yyyy-MM-dd")
  );
  const [timeValue, setTimeValue] = useState<number[]>([
    getCurrentTimePercentage(),
  ]);
  const [searchParamsUpdated, setSearchParamsUpdated] =
    useState<boolean>(false);
  const [nearByRadius, setNearByRadius] = useState<number>(
    NEARBY_DEFAULT_RADIUS_IN_METERS
  );

  // Deals loading states
  const [nearbyDeals, setNearbyDeals] = useState<Deal[]>([]);
  const [businessCount, setBusinessCount] = useState<number>(0);

  const { data: featuredDeals, isLoading: isLoadingFeatured } = useFeaturedDealsQuery();
  const { data: recentlyViewedDeals = [], isLoading: loadingRecentlyViewed } = useRecentlyViewedDealsQuery(user?.id);
  const { data: favoriteDeals = [], isLoading: loadingFavorites } = useFavoriteDealsQuery(user?.id);

  const getTimeFromPercentage = useCallback((percentage: number): string => {
    const totalMinutes = (percentage / 100) * 24 * 60;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = Math.floor(totalMinutes % 60);
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const resetToNow = useCallback(() => {
    setSelectedDate(format(new Date(), "yyyy-MM-dd"));
    setTimeValue([getCurrentTimePercentage()]);
    setSearchParamsUpdated(true);
  }, []);

  // Utilizziamo il nuovo hook per la ricerca delle offerte vicine
  const {
    results: nearbyDealsResults,
    isLoading,
    updateSearchParams,
    metrics,
  } = useNearbyDealsSearch({
    date: selectedDate,
    time: getTimeFromPercentage(timeValue[0]),
    location: userLocation
      ? { lat: userLocation.lat, lng: userLocation.lng, radius: nearByRadius }
      : undefined,
  });

  useEffect(() => {
    if (searchParamsUpdated) {
      const timeString = getTimeFromPercentage(timeValue[0]);
      updateSearchParams({
        date: selectedDate,
        time: timeString,
        location: userLocation
          ? {
              lat: userLocation.lat,
              lng: userLocation.lng,
              radius: nearByRadius,
            }
          : undefined,
      });
      setSearchParamsUpdated(false);
    }
  }, [
    searchParamsUpdated,
    timeValue,
    selectedDate,
    getTimeFromPercentage,
    updateSearchParams,
  ]);

  useEffect(() => {
    setSearchParamsUpdated(true);
  }, [timeValue, selectedDate, nearByRadius]);

  // Aggiorniamo il conteggio delle attività disponibili
  useEffect(() => {
    if (nearbyDealsResults) {
      setBusinessCount(nearbyDealsResults.length);
    }
  }, [nearbyDealsResults]);

  // Trasformiamo i risultati nel formato accettato da DealCarousel
  useEffect(() => {
    if (nearbyDealsResults) {
      const transformedDeals = mapNearbyDealsToDealFormat(nearbyDealsResults);
      setNearbyDeals(transformedDeals);
    } else {
      setNearbyDeals([]);
    }
  }, [nearbyDealsResults]);


  const getSelectedDayName = (): string => {
    const today = format(new Date(), "yyyy-MM-dd");
    if (selectedDate === today) {
      return "oggi";
    }

    const date = new Date(selectedDate);
    const dayName = format(date, "EEEE", { locale: it });
    return dayName;
  };

  const handleDateSelect = useCallback((date: string) => {
    setSelectedDate(date);
    setSearchParamsUpdated(true);
  }, []);

  const handleTimeChange = useCallback((newValue: number[]) => {
    setTimeValue(newValue);
    setSearchParamsUpdated(true);
  }, []);

  return {
    // Time and date state
    selectedDate,
    timeValue,
    nearByRadius,
    setNearByRadius,

    // Selection handlers
    handleDateSelect,
    handleTimeChange,
    resetToNow,
    getTimeFromPercentage,
    getSelectedDayName,

    // Deals data
    nearbyDeals,
    recentlyViewedDeals,
    favoriteDeals,
    featuredDeals,

    // Loading states
    isLoading,
    loadingDeals: isLoading, // Usiamo isLoading dal nuovo hook
    loadingRecentlyViewed,
    loadingFavorites,
    loadingFeatured: isLoadingFeatured,

    // Stats
    businessCount,

    // Metriche di ricerca
    metrics,
  };
};
