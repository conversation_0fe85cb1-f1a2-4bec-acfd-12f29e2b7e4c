import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface BusinessAvailability {
  business_id: string;
  business_name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance: number | null;
  photos: string[] | null;
  category_id: string;
  available_slots: AvailableSlots[];
}

export interface AvailableSlots {
  exceptions: any[];
  schedule: DaySchedule[];
}

export interface DaySchedule {
  day: number;
  day_name: string;
  time_slots: TimeSlot[];
}

export interface TimeSlot {
  available_seats: number;
  end_time: string;
  start_time: string;
}

export interface SearchLocation {
  lat: number;
  lng: number;
  radius?: number;
}

export interface AvailabilitySearchParams {
  date: string;
  time?: string;
  location?: SearchLocation;
  cityName?: string;
  category_id?: string;
  cursor?: string;
  limit?: number;
}

export interface AvailabilitySearchResult {
  businesses: BusinessAvailability[];
  next_cursor: string | null;
  cached?: boolean;
}

export const useAvailabilitySearchQuery = (params: AvailabilitySearchParams) => {
  return useQuery({
    queryKey: ['availability-search', params.date, params.time, params.location, params.cityName, params.category_id, params.cursor, params.limit],
    queryFn: async (): Promise<AvailabilitySearchResult> => {
      if (!params.date) {
        throw new Error('Data richiesta per la ricerca disponibilità');
      }

      if (!params.location && !params.cityName) {
        throw new Error('Posizione o nome città richiesti per la ricerca disponibilità');
      }

      const { data, error } = await supabase.functions.invoke(
        'get-availability-by-location',
        {
          body: {
            date: params.date,
            time: params.time,
            location: params.location,
            cityName: params.cityName,
            category_id: params.category_id,
            cursor: params.cursor,
            limit: params.limit,
          },
        }
      );

      if (error) {
        throw new Error(`Errore nella ricerca disponibilità: ${error.message}`);
      }

      return {
        businesses: data.businesses || [],
        next_cursor: data.next_cursor || null,
        cached: data.cached || false,
      };
    },
    enabled: !!params.date && (!!params.location || !!params.cityName),
    staleTime: 1000 * 30, // 30 seconds - location/availability data needs frequent updates
    refetchOnWindowFocus: true,
  });
};
