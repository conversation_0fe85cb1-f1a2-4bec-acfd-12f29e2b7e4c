-- Create enum types for reminders
CREATE TYPE public.reminder_status_enum AS ENUM ('pending', 'sent', 'cancelled', 'failed');
CREATE TYPE public.reminder_type_enum AS ENUM ('appointment', 'task', 'booking', 'custom');
CREATE TYPE public.notification_method_enum AS ENUM ('push', 'email', 'sms', 'in_app');

-- Create reminders table
CREATE TABLE public.reminders (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    reminder_time TIMESTAMP WITH TIME ZONE NOT NULL,
    type reminder_type_enum NOT NULL DEFAULT 'custom',
    status reminder_status_enum NOT NULL DEFAULT 'pending',
    notification_method notification_method_enum NOT NULL DEFAULT 'push',
    
    -- Optional references to existing entities
    entity_type TEXT, -- 'booking', 'deal', 'task', etc.
    entity_id UUID,
    
    -- Reminder configuration
    advance_minutes INTEGER DEFAULT 15, -- How many minutes before the event
    repeat_interval TEXT, -- 'daily', 'weekly', 'monthly', 'none'
    max_attempts INTEGER DEFAULT 3,
    attempts INTEGER DEFAULT 0,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('utc'::text, now())
);

-- Enable Row Level Security
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own reminders" 
ON public.reminders 
FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own reminders" 
ON public.reminders 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reminders" 
ON public.reminders 
FOR UPDATE 
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reminders" 
ON public.reminders 
FOR DELETE 
USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_reminders_user_id ON public.reminders(user_id);
CREATE INDEX idx_reminders_reminder_time ON public.reminders(reminder_time);
CREATE INDEX idx_reminders_status ON public.reminders(status);
CREATE INDEX idx_reminders_entity ON public.reminders(entity_type, entity_id) WHERE entity_type IS NOT NULL AND entity_id IS NOT NULL;

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_reminders_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SET search_path = public;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_reminders_updated_at
    BEFORE UPDATE ON public.reminders
    FOR EACH ROW
    EXECUTE FUNCTION public.update_reminders_updated_at();