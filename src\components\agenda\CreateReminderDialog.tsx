import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { CalendarIcon, Plus } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/auth/useAuth";
import { toast } from "sonner";

interface CreateReminderDialogProps {
  onReminderCreated?: () => void;
}

const CreateReminderDialog = ({ onReminderCreated }: CreateReminderDialogProps) => {
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: new Date(),
    time: "09:00",
    type: "custom" as "appointment" | "task" | "booking" | "custom",
    notification_method: "push" as "push" | "email" | "sms" | "in_app",
    advance_minutes: 15,
    repeat_interval: "none"
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      // Combine date and time
      const [hours, minutes] = formData.time.split(':');
      const reminderDateTime = new Date(formData.date);
      reminderDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      const { error } = await supabase
        .from('reminders')
        .insert({
          user_id: user.id,
          title: formData.title,
          description: formData.description || null,
          reminder_time: reminderDateTime.toISOString(),
          type: formData.type,
          notification_method: formData.notification_method,
          advance_minutes: formData.advance_minutes,
          repeat_interval: formData.repeat_interval === 'none' ? null : formData.repeat_interval,
          status: 'pending'
        });

      if (error) {
        console.error('Errore nella creazione del promemoria:', error);
        toast.error('Errore nella creazione del promemoria');
        return;
      }

      toast.success('Promemoria creato con successo');
      setOpen(false);
      
      // Reset form
      setFormData({
        title: "",
        description: "",
        date: new Date(),
        time: "09:00",
        type: "custom",
        notification_method: "push",
        advance_minutes: 15,
        repeat_interval: "none"
      });

      // Notify parent to refresh the list
      if (onReminderCreated) {
        onReminderCreated();
      }
    } catch (error) {
      console.error('Errore:', error);
      toast.error('Errore nella creazione del promemoria');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button size="sm" className="h-8 px-2 text-xs whitespace-nowrap">
          <Plus className="w-3 h-3 mr-1" />
          <span className="hidden sm:inline">Nuovo</span>
          <span className="sm:hidden">+</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Crea Nuovo Promemoria</DialogTitle>
          <DialogDescription>
            Aggiungi un promemoria per non dimenticare i tuoi appuntamenti e attività
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="title">Titolo *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Inserisci il titolo del promemoria"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrizione</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Aggiungi una descrizione (opzionale)"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Data *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(formData.date, "dd MMM yyyy", { locale: it })}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={formData.date}
                    onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Orario *</Label>
              <Input
                id="time"
                type="time"
                value={formData.time}
                onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Tipo</Label>
              <Select
                value={formData.type}
                onValueChange={(value: "appointment" | "task" | "booking" | "custom") =>
                  setFormData(prev => ({ ...prev, type: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="appointment">Appuntamento</SelectItem>
                  <SelectItem value="task">Attività</SelectItem>
                  <SelectItem value="booking">Prenotazione</SelectItem>
                  <SelectItem value="custom">Personalizzato</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Notifica</Label>
              <Select
                value={formData.notification_method}
                onValueChange={(value: "push" | "email" | "sms" | "in_app") =>
                  setFormData(prev => ({ ...prev, notification_method: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="push">Push</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="in_app">In App</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Preavviso (minuti)</Label>
              <Select
                value={formData.advance_minutes.toString()}
                onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, advance_minutes: parseInt(value) }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 minuti</SelectItem>
                  <SelectItem value="10">10 minuti</SelectItem>
                  <SelectItem value="15">15 minuti</SelectItem>
                  <SelectItem value="30">30 minuti</SelectItem>
                  <SelectItem value="60">1 ora</SelectItem>
                  <SelectItem value="120">2 ore</SelectItem>
                  <SelectItem value="1440">1 giorno</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Ripetizione</Label>
              <Select
                value={formData.repeat_interval}
                onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, repeat_interval: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Nessuna</SelectItem>
                  <SelectItem value="daily">Giornaliera</SelectItem>
                  <SelectItem value="weekly">Settimanale</SelectItem>
                  <SelectItem value="monthly">Mensile</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={loading}
            >
              Annulla
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creazione...' : 'Crea Promemoria'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateReminderDialog;