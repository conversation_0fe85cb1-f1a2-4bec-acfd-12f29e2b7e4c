import { supabase } from "@/integrations/supabase/client";
import { format, parseISO } from "date-fns";
import { it } from "date-fns/locale";
import {
  welcomeBookingMessage,
  welcomeBusinessChatMessage,
} from "@/data/message-templates";
import { CATCH_UP_USER_ID } from "@/data/catchup";

export type ConversationRole = "customer" | "owner";

export interface CreateConversationParams {
  businessId: string;
  dealId: string;
  userId: string;
  welcomeMessageData: {
    businessName: string;
  };
}

export interface CreateBookingConversationParams {
  bookingId: string;
  businessId: string;
  dealId: string;
  businessOwnerId: string;
  customerId: string;
  // Welcome message data
  welcomeMessageData: {
    businessName: string;
    address: string;
    dealTitle: string;
    dealDescription: string;
    bookingDate: string;
    startTime: string;
    discountedPrice: number;
    discountPercentage: number;
    qrData: any;
  };
}

export interface ConversationResult {
  conversationId: string;
  isExisting: boolean;
}

export interface CreateConversationAIAssistantParams {
  userId: string;
  welcomeMessageData: {
    businessName: string;
  };
}

export const createOrGetPersonalAIAssistantChatConversation = async ({
  userId,
  welcomeMessageData,
}: CreateConversationAIAssistantParams): Promise<ConversationResult> => {
  
  // Check if conversation already exists
  const { data: existingConversation, error: searchError } = await supabase
    .from("conversations")
    .select("id")
    .is("business_id", null)
    .is("deal_id", null) //.not('deal_id', 'is', null);
    .eq("type", "personal_ai_assistant_chat")
    .eq("created_by", userId)
    .maybeSingle();

  if (searchError && searchError.code !== "PGRST116") {
    throw new Error(
      `Error searching for existing conversation: ${searchError.message}`
    );
  }

  if (existingConversation) {
    return {
      conversationId: existingConversation.id,
      isExisting: true,
    };
  }

  // Create new conversation
  const { data: conversation, error: conversationError } = await supabase
    .from("conversations")
    .insert({
      business_id: null,
      type: "personal_ai_assistant_chat",
      created_by: userId,
    })
    .select("id")
    .single();

  if (conversationError) {
    throw new Error(
      `Error creating conversation: ${conversationError.message}`
    );
  }

  // Add participants in parallel
  const participantInserts = [
    // Add customer as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: userId,
      role: "customer",
    }),
    // Add business owner as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: CATCH_UP_USER_ID,
      role: "owner",
    }),
  ];

  const participantResults = await Promise.all(participantInserts);

  // Check for errors in participant creation
  const participantErrors = participantResults
    .map((result) => result.error)
    .filter((error) => error !== null);

  if (participantErrors.length > 0) {
    throw new Error(
      `Error adding participants: ${participantErrors
        .map((e) => e.message)
        .join(", ")}`
    );
  }

  const welcomeMessage = welcomeBusinessChatMessage({
    businessName: welcomeMessageData.businessName,
  });

  const { error: messageError } = await supabase.from("messages").insert({
    conversation_id: conversation.id,
    user_id: CATCH_UP_USER_ID,
    content: welcomeMessage,
    metadata: {},
  });

  if (messageError) {
    throw new Error(`Error adding welcome message: ${messageError.message}`);
  }

  return {
    conversationId: conversation.id,
    isExisting: false,
  };
};

/**
 * Creates a conversation between a customer and business AI Assistant for the business information
 * @param param0
 * @returns
 */

export const createOrGetBusinessChatConversation = async ({
  businessId,
  userId,
  welcomeMessageData,
}: CreateConversationParams): Promise<ConversationResult> => {
  // Check if conversation already exists

  const { data: existingConversation, error: searchError } = await supabase
    .from("conversations")
    .select("id")
    .eq("business_id", businessId)
    .is("deal_id", null) //.not('deal_id', 'is', null);
    .eq("type", "business_chat")
    .eq("created_by", userId)
    .maybeSingle();

  if (searchError && searchError.code !== "PGRST116") {
    throw new Error(
      `Error searching for existing conversation: ${searchError.message}`
    );
  }

  if (existingConversation) {
    return {
      conversationId: existingConversation.id,
      isExisting: true,
    };
  }

  // Create new conversation
  const { data: conversation, error: conversationError } = await supabase
    .from("conversations")
    .insert({
      business_id: businessId,

      type: "business_chat",
      created_by: userId,
    })
    .select("id")
    .single();

  if (conversationError) {
    throw new Error(
      `Error creating conversation: ${conversationError.message}`
    );
  }

  // Get business owner ID
  const { data: businessOwner, error: businessOwnerError } = await supabase
    .from("businesses")
    .select("owner_id")
    .eq("id", businessId)
    .single();

  if (businessOwnerError) {
    throw new Error(
      `Error fetching business owner: ${businessOwnerError.message}`
    );
  }

  // Add participants in parallel
  const participantInserts = [
    // Add customer as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: userId,
      role: "customer",
    }),
    // Add business owner as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: businessOwner.owner_id,
      role: "owner",
    }),
  ];

  const participantResults = await Promise.all(participantInserts);

  // Check for errors in participant creation
  const participantErrors = participantResults
    .map((result) => result.error)
    .filter((error) => error !== null);

  if (participantErrors.length > 0) {
    throw new Error(
      `Error adding participants: ${participantErrors
        .map((e) => e.message)
        .join(", ")}`
    );
  }

  const welcomeMessage = welcomeBusinessChatMessage({
    businessName: welcomeMessageData.businessName,
  });

  const { error: messageError } = await supabase.from("messages").insert({
    conversation_id: conversation.id,
    user_id: businessOwner.owner_id,
    content: welcomeMessage,
    metadata: {},
  });

  if (messageError) {
    throw new Error(`Error adding welcome message: ${messageError.message}`);
  }

  return {
    conversationId: conversation.id,
    isExisting: false,
  };
};

/**
 * Creates or retrieves an existing conversation between a customer and business for a specific deal
 * @param params - The parameters for creating/finding the conversation
 * @returns Promise with conversation ID and whether it was existing or newly created
 */
export const createOrGetBusinessDealChatConversation = async ({
  businessId,
  dealId,
  userId,
  welcomeMessageData,
}: CreateConversationParams): Promise<ConversationResult> => {
  // Check if conversation already exists

  const { data: existingConversation, error: searchError } = await supabase
    .from("conversations")
    .select("id")
    .eq("business_id", businessId)
    .eq("deal_id", dealId)
    .eq("type", "business_chat")
    .eq("created_by", userId)
    .maybeSingle();

  if (searchError && searchError.code !== "PGRST116") {
    throw new Error(
      `Error searching for existing conversation: ${searchError.message}`
    );
  }

  if (existingConversation) {
    return {
      conversationId: existingConversation.id,
      isExisting: true,
    };
  }

  // Create new conversation
  const { data: conversation, error: conversationError } = await supabase
    .from("conversations")
    .insert({
      business_id: businessId,
      deal_id: dealId,
      type: "business_chat",
      created_by: userId,
    })
    .select("id")
    .single();

  if (conversationError) {
    throw new Error(
      `Error creating conversation: ${conversationError.message}`
    );
  }

  // Get business owner ID
  const { data: businessOwner, error: businessOwnerError } = await supabase
    .from("businesses")
    .select("owner_id")
    .eq("id", businessId)
    .single();

  if (businessOwnerError) {
    throw new Error(
      `Error fetching business owner: ${businessOwnerError.message}`
    );
  }

  // Add participants in parallel
  const participantInserts = [
    // Add customer as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: userId,
      role: "customer",
    }),
    // Add business owner as participant
    supabase.from("conversation_participants").insert({
      conversation_id: conversation.id,
      user_id: businessOwner.owner_id,
      role: "owner",
    }),
  ];

  const participantResults = await Promise.all(participantInserts);

  // Check for errors in participant creation
  const participantErrors = participantResults
    .map((result) => result.error)
    .filter((error) => error !== null);

  if (participantErrors.length > 0) {
    throw new Error(
      `Error adding participants: ${participantErrors
        .map((e) => e.message)
        .join(", ")}`
    );
  }

  const welcomeMessage = welcomeBusinessChatMessage({
    businessName: welcomeMessageData.businessName,
  });

  const { error: messageError } = await supabase.from("messages").insert({
    conversation_id: conversation.id,
    user_id: businessOwner.owner_id,
    content: welcomeMessage,
    metadata: {},
  });

  if (messageError) {
    throw new Error(`Error adding welcome message: ${messageError.message}`);
  }

  return {
    conversationId: conversation.id,
    isExisting: false,
  };
};

/**
 * Checks if a conversation exists between a user and business for a specific deal
 * @param params - The parameters for checking the conversation
 * @returns Promise with conversation ID if it exists, null otherwise
 */
export const getExistingConversation = async ({
  businessId,
  dealId,
  userId,
}: CreateConversationParams): Promise<string | null> => {
  const { data: conversation, error } = await supabase
    .from("conversations")
    .select("id")
    .eq("business_id", businessId)
    .eq("deal_id", dealId)
    .eq("type", "business_chat")
    .eq("created_by", userId)
    .maybeSingle();

  if (error && error.code !== "PGRST116") {
    throw new Error(
      `Error checking for existing conversation: ${error.message}`
    );
  }

  return conversation?.id || null;
};

/**
 * Creates a conversation specifically for a booking between customer and business
 * @param params - The parameters for creating the booking conversation
 * @returns Promise with conversation ID and participant information
 */
export const createBookingChatConversation = async ({
  bookingId,
  businessId,
  dealId,
  businessOwnerId,
  customerId,
  welcomeMessageData,
}: CreateBookingConversationParams): Promise<ConversationResult> => {
  // Create new booking conversation
  const { data: conversation, error: conversationError } = await supabase
    .from("conversations")
    .insert({
      booking_id: bookingId,
      business_id: businessId,
      deal_id: dealId,
      type: "booking",
      created_by: businessOwnerId,
    })
    .select("id")
    .single();

  if (conversationError) {
    throw new Error(
      `Error creating booking conversation: ${conversationError.message}`
    );
  }

  // Check for existing participants
  const { data: existingParticipants, error: participantsCheckError } =
    await supabase
      .from("conversation_participants")
      .select("user_id")
      .eq("conversation_id", conversation.id);

  if (participantsCheckError) {
    throw new Error(
      `Error checking existing participants: ${participantsCheckError.message}`
    );
  }

  const existingUserIds = new Set(
    existingParticipants?.map((p) => p.user_id) || []
  );

  // Prepare participants that don't already exist
  const participantsToAdd = [
    {
      conversation_id: conversation.id,
      user_id: customerId,
      role: "customer" as ConversationRole,
    },
    {
      conversation_id: conversation.id,
      user_id: businessOwnerId,
      role: "owner" as ConversationRole,
    },
  ].filter((p) => !existingUserIds.has(p.user_id));

  // Add participants if needed
  if (participantsToAdd.length > 0) {
    const { error: participantsError } = await supabase
      .from("conversation_participants")
      .insert(participantsToAdd);

    if (participantsError) {
      throw new Error(
        `Error adding participants: ${participantsError.message}`
      );
    }
  }

  // Create and add welcome message
  const formattedDate = format(
    parseISO(welcomeMessageData.bookingDate),
    "EEEE d MMMM yyyy",
    { locale: it }
  );

  const welcomeMessage = welcomeBookingMessage({
    businessName: welcomeMessageData.businessName,
    address: welcomeMessageData.address,
    formattedDate,
    title: welcomeMessageData.dealTitle,
    description: welcomeMessageData.dealDescription,
    startTime: welcomeMessageData.startTime,
    discounted_price: welcomeMessageData.discountedPrice,
    discount_percentage: welcomeMessageData.discountPercentage,
  });

  const { error: messageError } = await supabase.from("messages").insert({
    conversation_id: conversation.id,
    user_id: businessOwnerId,
    content: welcomeMessage,
    metadata: {
      ui: "booking-data",
      "qr-data": welcomeMessageData.qrData,
    },
  });

  if (messageError) {
    throw new Error(`Error adding welcome message: ${messageError.message}`);
  }

  return {
    conversationId: conversation.id,
    isExisting: false,
  };
};
