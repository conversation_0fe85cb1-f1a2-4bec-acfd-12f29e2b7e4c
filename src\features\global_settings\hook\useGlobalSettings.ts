import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

type GlobalSettingType = Database["public"]["Enums"]["global_setting_type"];

export type GlobalSettingProperty =
  | "ai_server"
  | "demo_map"
  | "demo_coordinates";

function castValue(
  value: string | null,
  type: GlobalSettingType
): string | number | boolean | null {
  if (value === null || value === undefined) return null;

  switch (type) {
    case "number": {
      const n = Number(value);
      return Number.isNaN(n) ? null : n;
    }
    case "boolean": {
      const v = String(value).trim().toLowerCase();
      if (["true", "1", "yes", "y", "on"].includes(v)) return true;
      if (["false", "0", "no", "n", "off"].includes(v)) return false;
      // Fallback: try JSON parse for strict booleans, otherwise null
      try {
        const parsed = JSON.parse(v);
        if (typeof parsed === "boolean") return parsed;
      } catch {}
      return null;
    }
    case "string":
    default:
      return value;
  }
}

/**
 * Fetch a single global setting by its `property` and return its value
 * cast according to the row's `type` field.
 *
 * Usage:
 *   const { data, isLoading, error } = useGlobalSetting("my_property");
 *   // data is string | number | boolean | null
 */
export const useGlobalSetting = (property: GlobalSettingProperty) => {
  return useQuery({
    queryKey: ["global_setting", property],
    enabled: Boolean(property),
    staleTime: 1000 * 60 * 10, // 10 minutes
    refetchOnWindowFocus: false,
    queryFn: async () => {
      const { data, error, status } = await supabase
        .from("global_settings")
        .select("value, type")
        .eq("property", property)
        .maybeSingle();

      // Not found
      if (!data && (status === 406 || status === 200)) return null;
      if (error) throw new Error(error.message);

      return castValue(data?.value ?? null, data?.type as GlobalSettingType);
    },
  });
};

// Backwards-compatible alias if consumer expects plural naming
export const useGlobalSettings = useGlobalSetting;
