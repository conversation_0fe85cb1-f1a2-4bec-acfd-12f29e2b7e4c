import <PERSON><PERSON> from "lottie-react";
import { motion } from "framer-motion";

// You can replace these with actual Lottie JSON files from LottieFiles
const chatAnimation = {
  "v": "5.5.7",
  "fr": 60,
  "ip": 0,
  "op": 120,
  "w": 400,
  "h": 400,
  "nm": "Chat Animation",
  "ddd": 0,
  "assets": [],
  "layers": [
    {
      "ddd": 0,
      "ind": 1,
      "ty": 4,
      "nm": "Circle",
      "sr": 1,
      "ks": {
        "o": {"a": 0, "k": 100, "ix": 11},
        "r": {"a": 1, "k": [
          {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]},
          {"t": 120, "s": [360]}
        ], "ix": 10},
        "p": {"a": 0, "k": [200, 200], "ix": 2},
        "a": {"a": 0, "k": [0, 0], "ix": 1},
        "s": {"a": 1, "k": [
          {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 0, "s": [0, 0]},
          {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 30, "s": [100, 100]},
          {"i": {"x": [0.833, 0.833], "y": [0.833, 0.833]}, "o": {"x": [0.167, 0.167], "y": [0.167, 0.167]}, "t": 90, "s": [100, 100]},
          {"t": 120, "s": [0, 0]}
        ], "ix": 6}
      },
      "ao": 0,
      "shapes": [
        {
          "ty": "gr",
          "it": [
            {
              "d": 1,
              "ty": "el",
              "s": {"a": 0, "k": [80, 80], "ix": 2},
              "p": {"a": 0, "k": [0, 0], "ix": 3},
              "nm": "Ellipse Path 1",
              "mn": "ADBE Vector Shape - Ellipse",
              "hd": false
            },
            {
              "ty": "fl",
              "c": {"a": 0, "k": [0.2, 0.7, 0.4, 1], "ix": 4},
              "o": {"a": 0, "k": 100, "ix": 5},
              "r": 1,
              "bm": 0,
              "nm": "Fill 1",
              "mn": "ADBE Vector Graphic - Fill",
              "hd": false
            }
          ],
          "nm": "Ellipse 1",
          "np": 2,
          "cix": 2,
          "bm": 0,
          "ix": 1,
          "mn": "ADBE Vector Group",
          "hd": false
        }
      ],
      "ip": 0,
      "op": 120,
      "st": 0,
      "bm": 0
    }
  ]
};

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const EmptyConversationsState = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="flex flex-col items-center justify-center py-16 px-6"
    >
      {/* Chat Illustration */}
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="w-48 h-48 mb-6"
      >
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Chat bubbles illustration */}
          <div className="relative">
            <motion.div
              animate={{ 
                y: [0, -10, 0],
                rotate: [0, 5, 0]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-20 h-12 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl rounded-bl-sm relative shadow-lg"
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex space-x-1">
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                  <motion.div
                    animate={{ opacity: [0.4, 1, 0.4] }}
                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.4 }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                </div>
              </div>
            </motion.div>
            
            <motion.div
              animate={{ 
                y: [0, 8, 0],
                rotate: [0, -3, 0]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1.5
              }}
              className="w-16 h-10 bg-gradient-to-r from-gray-100 to-gray-200 rounded-2xl rounded-br-sm mt-3 ml-8 shadow-md"
            >
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="flex space-x-1">
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="text-center max-w-sm"
      >
        <h3 className="text-xl font-semibold text-gray-900 mb-3">
          Inizia a chattare!
        </h3>
        <p className="text-gray-600 mb-6 leading-relaxed flex flex-col items-center justify-center">
          <span className="text-gray-500 mb-5">Le tue conversazioni con i business appariranno qui.</span>
         
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-2 h-2 bg-green-500 rounded-full"
          />
          <span>Contatta un&apos;attività per iniziare a chattare.</span>
        </div>
        
        
      </motion.div>
    </motion.div>
  );
};

export const EmptyNotificationsState = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="flex flex-col items-center justify-center py-16 px-6"
    >
      {/* Notification Illustration */}
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="w-48 h-48 mb-6"
      >
        <div className="relative w-full h-full flex items-center justify-center">
          {/* Phone with notifications */}
          <div className="relative">
            <motion.div
              animate={{ 
                rotateY: [0, 10, 0],
              }}
              transition={{ 
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-24 h-40 bg-gradient-to-b from-gray-800 to-gray-900 rounded-3xl p-2 shadow-2xl"
            >
              <div className="w-full h-full bg-gradient-to-b from-blue-50 to-blue-100 rounded-2xl relative overflow-hidden">
                {/* Screen content */}
                <div className="absolute top-4 left-0 right-0">
                  {/* Notification badges */}
                  <motion.div
                    animate={{ 
                      scale: [1, 1.2, 1],
                      rotate: [0, 15, 0]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      delay: 0
                    }}
                    className="absolute top-0 right-2 w-4 h-4 bg-gradient-to-r from-orange-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg"
                  >
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </motion.div>
                  
                  <motion.div
                    animate={{ 
                      scale: [1, 1.1, 1],
                      rotate: [0, -10, 0]
                    }}
                    transition={{ 
                      duration: 2.5,
                      repeat: Infinity,
                      delay: 0.8
                    }}
                    className="absolute top-3 left-2 w-3 h-3 bg-gradient-to-r from-green-400 to-green-500 rounded-full shadow-md"
                  />
                  
                  <motion.div
                    animate={{ 
                      scale: [1, 1.3, 1],
                      y: [0, -5, 0]
                    }}
                    transition={{ 
                      duration: 1.8,
                      repeat: Infinity,
                      delay: 1.2
                    }}
                    className="absolute top-6 right-4 w-2 h-2 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full shadow-sm"
                  />
                </div>
              </div>
            </motion.div>
            
            {/* Floating notification icons */}
            <motion.div
              animate={{ 
                y: [0, -15, 0],
                x: [0, 10, 0],
                rotate: [0, 15, 0]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute -top-2 -right-8 w-8 h-8 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg flex items-center justify-center shadow-lg"
            >
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 16a2 2 0 001.985-1.75c.017-.137-.097-.25-.235-.25h-3.5c-.138 0-.252.113-.235.25A2 2 0 008 16z"/>
                <path fillRule="evenodd" d="M8 2a6 6 0 00-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 00.515 1.076 32.91 32.91 0 003.256.508 5.5 5.5 0 0010.972 0 32.91 32.91 0 003.256-.508.75.75 0 00.515-1.076A11.448 11.448 0 0114 8a6 6 0 00-6-6z" clipRule="evenodd"/>
              </svg>
            </motion.div>
            
            <motion.div
              animate={{ 
                y: [0, 12, 0],
                x: [0, -8, 0],
                rotate: [0, -12, 0]
              }}
              transition={{ 
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
              className="absolute -bottom-1 -left-6 w-6 h-6 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center shadow-lg"
            >
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"/>
              </svg>
            </motion.div>
          </div>
        </div>
      </motion.div>

      {/* Content */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="text-center max-w-sm"
      >
        <h3 className="text-xl font-semibold text-gray-900 mb-3">
          Resta sempre aggiornato!
        </h3>
        <p className="text-gray-600 mb-6 leading-relaxed">
          Riceverai qui notifiche su nuove offerte, aggiornamenti sulle prenotazioni e messaggi importanti dai business.
        </p>
        
        <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-2 h-2 bg-green-500 rounded-full"
          />
          <span>Ti avviseremo quando arriverà qualcosa</span>
        </div>
      </motion.div>
    </motion.div>
  );
}; 