import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { BookingWithDetails } from '@/types/booking';
import { parseBookingFromDB } from '@/types/booking';

export const useBusinessBookingsQuery = (businessId: string | undefined) => {
  return useQuery({
    queryKey: ['business-bookings', businessId],
    queryFn: async (): Promise<BookingWithDetails[]> => {
      if (!businessId) {
        return [];
      }

      const { data: deals, error: dealsError } = await supabase
        .from('deals')
        .select('id')
        .eq('business_id', businessId);

      if (dealsError) {
        throw new Error(`Errore nel recupero delle offerte: ${dealsError.message}`);
      }

      if (!deals || deals.length === 0) {
        return [];
      }

      const dealIds = deals.map(deal => deal.id);

      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          *,
          deals (
            title,
            images,
            description,
            discounted_price,
            businesses (
              name,
              address
            )
          )
        `)
        .in('deal_id', dealIds)
        .order('booking_date', { ascending: true });

      if (bookingsError) {
        throw new Error(`Errore nel recupero delle prenotazioni: ${bookingsError.message}`);
      }

      if (!bookingsData || bookingsData.length === 0) {
        return [];
      }

      const userIds = bookingsData.map(booking => booking.user_id);
      const { data: usersData, error: usersError } = await supabase
        .from('user_details')
        .select('id, first_name, last_name, avatar_url')
        .in('id', userIds);

      if (usersError) {
        throw new Error(`Errore nel recupero dei dettagli utente: ${usersError.message}`);
      }

      const usersMap = (usersData || []).reduce((acc, user) => {
        acc[user.id] = user;
        return acc;
      }, {} as Record<string, any>);

      const bookingsWithUsers = bookingsData.map(booking => parseBookingFromDB({
        ...booking,
        user_details: usersMap[booking.user_id] || null
      }));

      return bookingsWithUsers;
    },
    enabled: !!businessId,
    staleTime: 1000 * 60 * 1, // 1 minute - bookings are frequently updated
    refetchOnWindowFocus: true,
  });
};
