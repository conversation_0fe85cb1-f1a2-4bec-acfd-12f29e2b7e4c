import { format } from "date-fns";
import { it } from "date-fns/locale";
import { Calendar, Clock, Bell, AlertCircle, X, Edit, Trash2 } from "lucide-react";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>le, SheetDescription } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface Reminder {
  id: string;
  title: string;
  description?: string;
  reminder_time: string;
  type: 'appointment' | 'task' | 'booking' | 'custom';
  status: 'pending' | 'sent' | 'cancelled' | 'failed';
  notification_method: 'push' | 'email' | 'sms' | 'in_app';
  entity_type?: string;
  entity_id?: string;
  advance_minutes: number;
  repeat_interval?: string;
  created_at: string;
}

interface ReminderBottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  date: Date | null;
  reminders: Reminder[];
}

const ReminderBottomSheet = ({ isOpen, onClose, date, reminders }: ReminderBottomSheetProps) => {
  if (!date) return null;

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'appointment':
        return <Calendar className="h-5 w-5" />;
      case 'task':
        return <Clock className="h-5 w-5" />;
      case 'booking':
        return <Bell className="h-5 w-5" />;
      default:
        return <AlertCircle className="h-5 w-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'sent':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'cancelled':
        return 'bg-slate-100 text-slate-600 border-slate-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'In attesa';
      case 'sent':
        return 'Inviato';
      case 'cancelled':
        return 'Annullato';
      case 'failed':
        return 'Fallito';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'appointment':
        return 'Appuntamento';
      case 'task':
        return 'Attività';
      case 'booking':
        return 'Prenotazione';
      case 'custom':
        return 'Personalizzato';
      default:
        return type;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[80vh] rounded-t-xl">
        <SheetHeader className="pb-4">
          <SheetTitle className="text-xl font-semibold">
            {format(date, 'EEEE d MMMM', { locale: it })}
          </SheetTitle>
          <SheetDescription>
            {reminders.length === 0 
              ? "Nessun promemoria per questo giorno"
              : `${reminders.length} promemori${reminders.length !== 1 ? '' : 'o'} programmati`
            }
          </SheetDescription>
        </SheetHeader>
        
        <div className="flex-1 overflow-y-auto pb-6">
          {reminders.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-slate-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-4">
                <Calendar className="h-10 w-10 text-slate-400" />
              </div>
              <p className="text-slate-600 mb-2">Giornata libera</p>
              <p className="text-slate-500 text-sm">Nessun promemoria per questo giorno</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reminders
                .sort((a, b) => new Date(a.reminder_time).getTime() - new Date(b.reminder_time).getTime())
                .map((reminder, index) => (
                  <div key={reminder.id}>
                    <div className="bg-slate-50 rounded-xl p-4 hover:bg-slate-100 transition-colors">
                      {/* Header */}
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="bg-white rounded-lg p-2 shadow-sm">
                            {getTypeIcon(reminder.type)}
                          </div>
                          <div>
                            <h3 className="font-semibold text-slate-900">{reminder.title}</h3>
                            <p className="text-sm text-slate-600">{getTypeText(reminder.type)}</p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-red-600 hover:text-red-700">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Time and Status */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="bg-white rounded-lg px-3 py-1.5 shadow-sm">
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4 text-slate-500" />
                            <span className="font-mono text-sm font-medium">
                              {format(new Date(reminder.reminder_time), 'HH:mm')}
                            </span>
                          </div>
                        </div>
                        <Badge variant="outline" className={cn("border", getStatusColor(reminder.status))}>
                          {getStatusText(reminder.status)}
                        </Badge>
                      </div>

                      {/* Description */}
                      {reminder.description && (
                        <div className="mb-3">
                          <p className="text-sm text-slate-700 bg-white rounded-lg p-3">
                            {reminder.description}
                          </p>
                        </div>
                      )}

                      {/* Notification Method */}
                      <div className="flex items-center justify-between text-xs text-slate-500">
                        <span>Notifica: {reminder.notification_method}</span>
                        {reminder.advance_minutes > 0 && (
                          <span>{reminder.advance_minutes} min prima</span>
                        )}
                      </div>
                    </div>
                    
                    {index < reminders.length - 1 && <Separator className="my-4" />}
                  </div>
                ))}
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ReminderBottomSheet;