-- Create table if it doesn't exist and align schema to user-centric auths (no business_id)
CREATE TABLE IF NOT EXISTS public.integration_authorizations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  provider text NOT NULL,
  integration_id text,
  status text,
  is_active boolean NOT NULL DEFAULT true,
  metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Remove business_id if present
ALTER TABLE public.integration_authorizations
  DROP COLUMN IF EXISTS business_id;

-- Ensure required columns exist
ALTER TABLE public.integration_authorizations
  ADD COLUMN IF NOT EXISTS provider text,
  ADD COLUMN IF NOT EXISTS integration_id text,
  ADD COLUMN IF NOT EXISTS status text,
  ADD COLUMN IF NOT EXISTS is_active boolean DEFAULT true,
  ADD COLUMN IF NOT EXISTS metadata jsonb DEFAULT '{}'::jsonb,
  ADD COLUMN IF NOT EXISTS created_at timestamptz DEFAULT now(),
  ADD COLUMN IF NOT EXISTS updated_at timestamptz DEFAULT now();

-- Make provider NOT NULL
ALTER TABLE public.integration_authorizations
  ALTER COLUMN provider SET NOT NULL;

-- Unique index to support ON CONFLICT (user_id, provider)
CREATE UNIQUE INDEX IF NOT EXISTS uniq_integration_authorizations_user_provider
  ON public.integration_authorizations (user_id, provider);

-- Enable RLS
ALTER TABLE public.integration_authorizations ENABLE ROW LEVEL SECURITY;

-- Policies: users manage their own records
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename = 'integration_authorizations'
      AND policyname = 'Users can view their own integrations'
  ) THEN
    CREATE POLICY "Users can view their own integrations"
      ON public.integration_authorizations
      FOR SELECT
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename = 'integration_authorizations'
      AND policyname = 'Users can insert their own integrations'
  ) THEN
    CREATE POLICY "Users can insert their own integrations"
      ON public.integration_authorizations
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename = 'integration_authorizations'
      AND policyname = 'Users can update their own integrations'
  ) THEN
    CREATE POLICY "Users can update their own integrations"
      ON public.integration_authorizations
      FOR UPDATE
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE schemaname = 'public'
      AND tablename = 'integration_authorizations'
      AND policyname = 'Users can delete their own integrations'
  ) THEN
    CREATE POLICY "Users can delete their own integrations"
      ON public.integration_authorizations
      FOR DELETE
      USING (auth.uid() = user_id);
  END IF;
END$$;

-- Trigger to keep updated_at fresh
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger
    WHERE tgname = 'trg_update_integration_authorizations_updated_at'
  ) THEN
    CREATE TRIGGER trg_update_integration_authorizations_updated_at
    BEFORE UPDATE ON public.integration_authorizations
    FOR EACH ROW
    EXECUTE FUNCTION public.update_integration_authorizations_updated_at();
  END IF;
END$$;