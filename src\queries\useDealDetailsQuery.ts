import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useDealDetailsQuery = (dealId: string | undefined) => {
  return useQuery({
    queryKey: ['deal-details', dealId],
    queryFn: async () => {
      if (!dealId) {
        throw new Error('Deal ID is required');
      }

      const { data, error } = await supabase
        .from('deals')
        .select(`
          *,
          businesses (
            name,
            address
          )
        `)
        .eq('id', dealId)
        .single();

      if (error) {
        throw new Error(`Errore nel recupero dell'offerta: ${error.message}`);
      }

      return data;
    },
    enabled: !!dealId,
    staleTime: 1000 * 60 * 5, // 5 minutes - deal details change moderately
    refetchOnWindowFocus: false,
  });
};
