# CatchUp Codebase Knowledge Base (RAG‑Ready)

Purpose: Provide a concise, structured knowledge source for retrieval‑augmented chat/voice assistants working on the CatchUp app. Content summarizes architecture, key modules, APIs, types, and flows referenced directly from the codebase.

---

## 1) System Overview

- Domain: Multi‑tenant marketplace connecting users to local business deals and bookings, with search, maps, chat/voice assistants, groups/social, reviews, and PWA support.
- Frontend: React + TypeScript (Vite, Tailwind, shadcn‑ui, Zustand, React Query, React Router).
- Backend: Supabase (Postgres, Auth, Storage, Edge Functions) + PostGIS RPCs, external APIs (OpenAI, ElevenLabs, Google Maps, OpenWeather, Replicate, Composio).
- Assistants: Text (LangGraph server) and Real‑time Voice (ElevenLabs) with custom prompts and tool orchestration via n8n/Edge functions.

Key entry points:
- App shell, routes, providers: `src/App.tsx:1`
- Bootstrap: `src/main.tsx:1`
- Supabase client: `src/integrations/supabase/client.ts:1`

---

## 2) Tech Stack

- React 18, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, shadcn‑ui components
- Routing: `react-router-dom`
- Data: `@tanstack/react-query`, `zustand`
- Maps: `@react-google-maps/api`, `@vis.gl/react-google-maps`, `@googlemaps/markerclusterer`
- Supabase: client + Edge Functions
- LLMs/Voice: `@langchain/*`, LangGraph SDK, OpenAI, ElevenLabs
- PWA + Push: `vite-plugin-pwa` and custom SW

Package manifest: `package.json:1`

---

## 3) Runtime & Configuration

- Supabase client and anon key: `src/integrations/supabase/client.ts:1`
- Edge Function secrets used across functions:
  - `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`
  - `OPENAI_API_KEY` (OpenAI)
  - `ELEVEN_LABS_API_KEY` (TTS)
  - `GOOGLE_MAPS_API_KEY`
  - `OPENWEATHER_API_KEY`
  - `REPLICATE_API_TOKEN` (images)
  - `COMPOSIO_API_KEY` (Gmail integration)
- PWA SW for push: `public/sw-push.js:1`

---

## 4) App Shell & Routing

- File: `src/App.tsx:1`
- Providers: React Query, A/B test, Theme, VoiceDialog, Tooltip, PWA prompts, LocationProvider
- Auth gating: `useAuth()` locks protected routes
- Notable routes:
  - Public: `/`, `/profile`, `/deals`, `/deals/:id`, `/map`, `/business/:id`, `/business-view/:businessId`, `/onboarding`
  - Auth required: bookings (`/book/:id`, `/confirm-booking`), business dashboard & management, groups (`/groups`, `/groups/:id`), chat (`/chat-catchup`, `/conversations`)
  - Test/dev: `/chat` (LangGraph), `/voice-navigation-test`, `/availabilitytest`, `/push-notification-test`

---

## 5) State Management

- Contexts
  - Location: `src/contexts/LocationContext.tsx:1` (subscribes to locationService)
  - VoiceDialog: `src/contexts/VoiceDialogContext.tsx:1` (global voice dialog state)
  - A/B Test: `src/contexts/ABTestContext.tsx:1` (color palette variant + tracking)
- Stores (Zustand)
  - Location store: `src/stores/useLocationStore.ts:1` (demo mode, geolocation, save/load settings via Supabase)
  - Settings store: `src/stores/useSettingsStore.ts:1` (voice/chat UI prefs)
- Data fetching: React Query hooks under `src/queries/*` (e.g., deals, availability)

---

## 6) Data Model (Supabase)

Typed DB schema: `src/integrations/supabase/types.ts:1`

Primary entities (subset):
- Deals: `deals`
  - Fields: `id`, `title`, `description`, `images[]`, pricing (`original_price`, `discounted_price`, `discount_percentage`), availability window (`start_date`, `end_date`), `time_slots` (JSON weekly schedule), `status` (`draft|published|expired`), `business_id`, `category_id`, `auto_confirm`, `fake`, `terms_conditions`, `created_at`, `updated_at`.
- Bookings: `bookings`
  - Fields: `id`, `deal_id`, `user_id`, `booking_date`, `booking_time`, `booking_end_time`, `qr_data` (JSON), `discounted_price`, `original_price`, `discount_percentage`, `status`, timestamps.
- Time Slot Bookings: `time_slot_bookings`
  - Tracks per‑slot seat usage: `deal_id`, `booking_id`, `booking_date`, `day_of_week`, `start_time`, `end_time`, `booked_seats`.
- Businesses & Categories: `businesses`, `categories`, `businesses_with_counts`, `business_product_categories`, `business_products`
- Users: `auth.users`, `user_details`, `user_preferences`, `user_location_history`
- Social/Groups: `groups`, `group_members`, `group_deal_shares`, `group_booking_shares`, `group_invites`
- Conversations: `conversations`, `messages`, `conversation_participants`, `conversations_thread`, plus `agent_*` tables for AI conversations
- Reviews: `reviews` (+ `review_sentiment` enum)
- Settings/Integrations: `global_settings`, `integration_authorizations`, `notifications`

Key type helpers:
- Deals (FE): `src/types/deals.ts:1`
- Booking (FE): `src/types/booking.ts:1`
- Conversations: `src/types/conversations.ts:1`
- Groups: `src/types/groups.ts:1`
- Notifications: `src/types/notifications.ts:1`

---

## 7) Edge Functions / API Endpoints (Supabase)

All under `supabase/functions/*`.

- get‑nearest‑businesses — `supabase/functions/get-nearest-businesses/index.ts:1`
  - Input: `{ latitude, longitude, limit?, transportMode?, useDirections?, maxDistance_meters?, require_deals? }`
  - Calls PostGIS RPC `get_nearest_businesses_postgis`, returns `{ businesses: [{ id, name, lat, lng, deal_count, address, distance, distanceText, photos[] }], nearestBusiness }`.

- get‑availability‑by‑location — `supabase/functions/get-availability-by-location/index.ts:1`
  - Input: `{ date: ISO, time?, location?: {lat,lng,radius?}, cityName?, category_id?, cursor?, limit? }`
  - RPC: `get_available_businesses`, returns `{ date, time?, businesses: [{ business_id, business_name, address, lat, lng, distance, photos, category_id, available_slots }], next_cursor }`.

- get‑nearby‑deals‑by‑location — `supabase/functions/get-nearby-deals-by-location/index.ts:1`
  - Input: same shape as availability; RPC: `get_nearby_deals`
  - Returns deals list with embedded business, per‑deal `available_slots`, `next_cursor`.

- generate‑deal‑content — `supabase/functions/generate-deal-content/index.ts:1`
  - OpenAI Chat Completions (structured tool output) to produce `{ title, description, serviceDetails, suggestedPrice }`.

- text‑to‑speech — `supabase/functions/text-to-speech/index.ts:1`
  - ElevenLabs TTS; Input: `{ text, voiceId? }`, Output: `{ audio: base64 }`.

- get‑google‑maps‑key — `supabase/functions/get-google-maps-key/index.ts:1`
  - Returns `{ GOOGLE_MAPS_API_KEY }` for client, with CORS and auth header propagation.

- get‑weather‑data — `supabase/functions/get-weather-data/index.ts:1`
  - OpenWeather current + 5‑day forecast; Input: `{ lat, lon }`, returns normalized weather block.

- generate‑business‑image — `supabase/functions/generate-business-image/index.ts:1`
  - Replicate (Flux) image generation; Input: `{ prompt, businessType?, businessName? }`, returns `{ imageUrl | id,status }`.

- analyze‑review — `supabase/functions/analyze-review/index.ts:1`
  - OpenAI moderation + sentiment; Input: `{ comment, rating }`, Output: `{ approved|blocked, sentiment }`.

- composio‑gmail — `supabase/functions/composio-gmail/index.ts:1`
  - Actions: `connect | callback | check_status | fetch_emails | send_email | disconnect`
  - Persists to `integration_authorizations` and integrates with Composio.

Shared CORS helper: `supabase/functions/_shared/cors.ts:1`

---

## 8) Search & Availability (FE)

- Nearby Availability Hook: `src/queries/useAvailabilitySearchQuery.ts:1`
  - Invokes `get-availability-by-location` function and returns `{ businesses[], next_cursor, cached }`.
- Nearby Deals Hook: `src/queries/useDealsQuery.ts:1` and `useBusinessesWithDealsQuery`
- Map UI: `src/components/map/*` (custom markers, clusters, navigation, selected marker, info slide)

---

## 9) Booking Flow

- Page: `src/pages/bookings/BookingPage.tsx:1`
  - Fetch deal, allow date selection (via `DateAvailabilitySelector`), compute availability, choose quantity.
  - On confirm:
    1) Insert into `bookings` with pricing and `qr_data`.
    2) Insert into `time_slot_bookings` with `booked_seats` for selected slot.
- Confirm/Details pages: `src/pages/bookings/*`

---

## 10) Deals List & Details

- List page: `src/pages/deals/DealsListingPage.tsx:1` using `useDealsQuery`
- Detail page: `src/pages/deals/DealDetailPage.tsx:1` (per‑deal display and booking path)
- Core UI: `src/components/deals/core/*` (card, pricing, carousel, main info)

---

## 11) Assistants: Voice & Chat

- Voice Dialog component: `src/features/voiceai/ui/VoiceDialog.tsx:1` (UI, state, navigation)
- Conversation hook: `src/features/voiceai/hooks/useVoiceConversation.ts:1`
  - Uses ElevenLabs `useConversation` for real‑time voice.
  - Builds structured prompts with user/location context and n8n tool protocol.
  - Writes/reads user thread IDs to `user_details` and queries Supabase as needed.
- Global Voice dialog wiring: `src/features/voiceai/ui/GlobalVoiceDialog.tsx:1` + `src/contexts/VoiceDialogContext.tsx:1`
- Chat (LangGraph) test client: `src/pages/test/LangGraphTest.tsx:1`
  - Streams via `@langchain/langgraph-sdk/react` to remote assistantId "catchup".
  - Extracts `llmResponse` from JSON message payloads.
- App route for voice assistant: `src/pages/CatchUpAgentChat.tsx:1`

Prompts (LLM instructions):
- Chat prompt: `prompts/prompt_chat.xml:1`
- Voice prompt: `prompts/prompt_voice.xml:1`
- ElevenLabs config notes: `prompts/eleven_labs.txt:1`

---

## 12) Notifications & PWA

- PWA prompts and update banner: `src/components/pwa/*`
- Service Worker for Push: `public/sw-push.js:1`
- Cache manager: `src/utils/cacheManager.ts:1` (cleanup, preload, versioning; initialized in `App.tsx`)

---

## 13) Admin & A/B Testing

- Admin Data Generator: `src/pages/admin/AdminDataPage.tsx:1`
- A/B testing context and tracker: `src/contexts/ABTestContext.tsx:1`, `src/components/ABTestTracker.tsx:1`
- Settings and subscription pages: `src/pages/business/SubscriptionPage.tsx:1`

---

## 14) External Integrations

- Google Maps Key delivery (Edge): `supabase/functions/get-google-maps-key/index.ts:1`
- Weather (Edge): `supabase/functions/get-weather-data/index.ts:1`
- TTS (ElevenLabs) (Edge): `supabase/functions/text-to-speech/index.ts:1`
- OpenAI deal content (Edge): `supabase/functions/generate-deal-content/index.ts:1`
- Replicate image gen (Edge): `supabase/functions/generate-business-image/index.ts:1`
- Gmail via Composio (Edge): `supabase/functions/composio-gmail/index.ts:1`

---

## 15) Common Types (FE)

- Deal: `src/types/deals.ts:1`
  - `DealFromDB`: pricing, dates, `time_slots` weekly schedule (with `schedule: DaySchedule[]` and `exceptions: string[]`), status, images, business link.
- Booking: `src/types/booking.ts:1` with `Booking`, `BookingWithDetails`, `QRData`.
- Availability/Map: `src/queries/useAvailabilitySearchQuery.ts:1` exports `BusinessAvailability`, `AvailableSlots`, `DaySchedule`, `TimeSlot`.
- Notifications: `src/types/notifications.ts:1`

---

## 16) Search, Schedules & Availability Concepts

- Weekly schedules:
  - `DaySchedule` → `{ day, day_name, time_slots[] }`
  - Time slots have `start_time`, `end_time`, `available_seats` (capacity per slot)
  - `exceptions[]`: ISO‑date strings to exclude
- Availability search factors:
  - Date/time filters
  - Distance/radius (meters)
  - Category filtering
  - Pagination via `cursor`, `limit`

---

## 17) Error Handling & Guardrails

- Prompts enforce:
  - Do not fabricate IDs; fetch via tools/DB (`prompts/prompt_chat.xml:1`)
  - Ask clarifying questions for ambiguous dates; never expose internal keys
- Edge Functions:
  - CORS preflight handling (`OPTIONS`)
  - Input validation; `400` for bad params; `500` on server errors
- FE Queries:
  - React Query retries and error surfaces; toasts for user feedback (e.g., booking pages)

---

## 18) Key User Flows

- Discover deals → Map/List → Detail → Book → Confirm
- Voice: Open VoiceDialog → start conversation → n8n tools → read deals/bookings → navigate or act
- Chat: LangGraph conversation with tool call visualizer (dev/test)
- Admin: seed/generate data; A/B testing variant logging

---

## 19) File Landmarks

- Routes/App: `src/App.tsx:1`, `src/main.tsx:1`
- Supabase types and client: `src/integrations/supabase/types.ts:1`, `src/integrations/supabase/client.ts:1`
- Queries: `src/queries/*`
- Deals UI: `src/components/deals/core/*`
- Maps UI: `src/components/map/*`
- Voice AI: `src/features/voiceai/*`
- Prompts: `prompts/*`
- PWA: `src/components/pwa/*`, `public/sw-push.js:1`
- Edge Functions: `supabase/functions/*`
- DB migrations and docs: `supabase/migrations/*`, `supabase/README-Edge-Functions.md:1`

---

## 20) Extension Tips (for Assistants)

- Retrieval strategy: chunk this doc by section; augment with specific file reads for implementation‑level questions.
- For data answers, prefer querying Supabase (typed) via existing hooks or Edge Functions before reasoning.
- For deal/booking IDs: never invent; fetch by name/date/category via tools or RPCs.
- Respect auth gating in routes; redirect unauthenticated users to `/login`.
- When in doubt on availability, recompute via Edge Functions with precise params (date, time, radius, category).

---

## 21) Glossary

- Deal: A promotable offer with a schedule and pricing.
- Booking: A user reservation for a deal at a specific date/time slot.
- Time Slot: Availability window with capacity; linked to bookings via `time_slot_bookings`.
- Business: Provider of deals; geo‑located and categorized.
- Availability Search: RPC‑backed query combining location, date/time, and category.
- Voice Assistant: ElevenLabs real‑time conversation client orchestrating tool calls via n8n.
- LangGraph Chat: Text assistant backed by remote LangGraph server.

---

## 22) Related Docs in Repo

- README and feature notes: `README.md:1`
- PWA/Notifications design: `docs/PWA_Implementation.md:1`, `docs/NotificationSystemDesign.md:1`
- Booking workflow: `docs/BookingWorkflow.md:1`
- Search panels: `src/components/search/README.md:1`
- Supabase Edge Function docs & diagrams: `supabase/README-Edge-Functions.md:1`, `supabase/supabase-edge-functions-documentation.md:1`, `supabase/supabase-edge-functions-uml-diagrams.md:1`

---

Last updated: regenerate this file when routes, types, or Edge Functions change.

