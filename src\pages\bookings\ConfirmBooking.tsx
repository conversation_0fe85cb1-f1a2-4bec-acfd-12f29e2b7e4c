import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Database } from "@/integrations/supabase/types";
import { useAuth } from "@/hooks/auth/useAuth";
import { useCreateBooking } from "@/hooks/booking/useCreateBooking";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import BookingDetails from "@/components/booking/BookingDetails";
import BookingImportantInfo from "@/components/booking/BookingImportantInfo";
import BookingFooter from "@/components/booking/BookingFooter";
import { BookingDetails as IBookingDetails } from "@/types/booking";
import { availabilityService } from "@/services/availabilityService";

type Deal = Database['public']['Tables']['deals']['Row'];
type Business = Database['public']['Tables']['businesses']['Row'];

interface DealWithBusiness extends Deal {
  businesses: Business;
}

const ConfirmBooking = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [deal, setDeal] = useState<DealWithBusiness | null>(null);
  const [availableSeats, setAvailableSeats] = useState<number | undefined>(undefined);

  // Recupera i dettagli della prenotazione dallo state della navigazione
  const bookingDetails = location.state?.bookingDetails as IBookingDetails;
  const dealId = location.state?.dealId as string;

  const { isLoading, handleConfirmBooking } = useCreateBooking(deal, bookingDetails, user?.id);

  useEffect(() => {
    const fetchDeal = async () => {
      if (!dealId) {
        navigate('/');
        return;
      }

      try {
        const { data, error } = await supabase
          .from('deals')
          .select(`
            *,
            businesses (
              id,
              name,
              address,
              owner_id
            )
          `)
          .eq('id', dealId)
          .maybeSingle();

        if (error || !data) {
          toast.error("Errore nel caricamento dell'offerta");
          navigate('/');
          return;
        }

        setDeal(data as DealWithBusiness);

        // Controlla la disponibilità di posti se abbiamo dettagli di prenotazione
        if (bookingDetails && bookingDetails.day && bookingDetails.start_time) {
          const dayOfWeek = bookingDetails.day;
          const startTime = bookingDetails.start_time;
          
          // Parse time_slots data safely
          const timeSlots = data.time_slots;
          let parsedTimeSlots;
          
          if (typeof timeSlots === 'string') {
            parsedTimeSlots = JSON.parse(timeSlots);
          } else {
            parsedTimeSlots = timeSlots;
          }
          
          if (parsedTimeSlots && parsedTimeSlots.schedule) {
            // Cerca lo slot orario specifico
            const daySchedule = parsedTimeSlots.schedule.find((day: any) => day.day === dayOfWeek);
            const timeSlot = daySchedule?.time_slots.find((slot: any) => 
              slot.start_time === startTime
            );
            
            if (timeSlot) {
              // Get the total available seats for this time slot
              const totalSeats = timeSlot.available_seats || 0;
              
              // Get bookings for this date and time slot
              const bookings = await availabilityService.getTimeSlotBookings(dealId, bookingDetails.date);
              
              // Get booked seats for this time slot (add ":00" to match the database format)
              const bookedSeats = bookings[startTime + ":00"] || 0;
              
              // Calculate remaining seats
              const remainingSeats = availabilityService.calculateRemainingSeats(totalSeats, bookedSeats);
              
              setAvailableSeats(remainingSeats);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching deal:', error);
        toast.error("Si è verificato un errore");
        navigate('/');
      }
    };

    fetchDeal();
  }, [dealId, navigate, bookingDetails]);

  if (!deal || !bookingDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-pulse">Caricamento...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader variant="booking" showBackButton={true} title="Conferma Prenotazione" />

      <main className="pt-16 pb-32 px-4">
        <BookingDetails 
          deal={deal}
          bookingDate={bookingDetails.date}
          bookingTime={bookingDetails.start_time}
        />
        <BookingImportantInfo />
      </main>

      <BookingFooter 
        onConfirm={handleConfirmBooking}
        isLoading={isLoading}
        availableSeats={availableSeats}
      />
    </div>
  );
};

export default ConfirmBooking;
