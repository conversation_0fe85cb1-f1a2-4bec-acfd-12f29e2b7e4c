import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useBusinessByIdQuery = (businessId: string | undefined) => {
  return useQuery({
    queryKey: ['business', businessId],
    queryFn: async () => {
      if (!businessId) {
        throw new Error('Business ID richiesto per il recupero del business');
      }

      const { data, error } = await supabase
        .from('businesses')
        .select('*')
        .eq('id', businessId)
        .single();

      if (error) {
        throw new Error(`Errore nel recupero del business: ${error.message}`);
      }

      return data;
    },
    enabled: !!businessId,
    staleTime: 1000 * 60 * 5, // 5 minutes - business data changes moderately
    refetchOnWindowFocus: false,
  });
};

export const useCategoryByIdQuery = (categoryId: string | undefined) => {
  return useQuery({
    queryKey: ['category', categoryId],
    queryFn: async () => {
      if (!categoryId) {
        throw new Error('Category ID richiesto per il recupero della categoria');
      }

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('id', categoryId)
        .single();

      if (error) {
        throw new Error(`Errore nel recupero della categoria: ${error.message}`);
      }

      return data;
    },
    enabled: !!categoryId,
    staleTime: 1000 * 60 * 30, // 30 minutes - categories rarely change
    refetchOnWindowFocus: false,
  });
};
