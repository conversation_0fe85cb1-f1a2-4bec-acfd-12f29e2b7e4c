import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { BookingWithDetails } from "@/types/booking";
import { useBusinessBookingsQuery } from "@/queries/useBusinessBookingsQuery";

export const useBusinessBookings = (businessId: string | undefined) => {
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);

  const { data: bookings = [], isLoading, refetch } = useBusinessBookingsQuery(businessId);

  const handleApprove = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'confirmed' })
        .eq('id', bookingId);

      if (error) throw error;
      toast.success("Prenotazione approvata con successo");
      refetch();
    } catch (error) {
      console.error('Error approving booking:', error);
      toast.error("Errore nell'approvazione della prenotazione");
    }
  };

  const handleCancel = async (bookingId: string, note: string) => {
    if (!note) {
      toast.error("Inserisci una nota per la cancellazione");
      return;
    }
    setIsCancelling(true);
    try {
      const { error } = await supabase
        .from('bookings')
        .update({
          status: 'cancelled',
          cancellation_note: note
        })
        .eq('id', bookingId);

      if (error) throw error;
      toast.success("Prenotazione cancellata con successo");
      setSelectedBookingId(null);
      refetch();
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error("Errore nella cancellazione della prenotazione");
    } finally {
      setIsCancelling(false);
    }
  };

  return {
    bookings,
    isLoading,
    selectedBookingId,
    setSelectedBookingId,
    isCancelling,
    handleApprove,
    handleCancel
  };
};
