import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useLocationUsersQuery, useLocationDataQuery, LocationHistoryData, UserData } from '@/queries/useLocationHistoryQuery';

export type { LocationHistoryData, UserData };

export const useLocationHistory = () => {
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedUser, setSelectedUser] = useState<string>('all');
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch users using React Query
  const { data: users = [], isLoading: isLoadingUsers, refetch: refetchUsers } = useLocationUsersQuery();

  // Fetch location data using React Query (only when date is selected)
  const { 
    data: locationData = [], 
    isLoading: isLoadingLocation, 
    refetch: refetchLocationData 
  } = useLocationDataQuery(selectedDate, selectedUser, users);

  const isLoading = isLoadingUsers || isLoadingLocation;

  const fetchLocationData = async (date: string, user: string = 'all') => {
    setSelectedDate(date);
    setSelectedUser(user);
    return locationData;
  };

  const fetchUsers = async () => {
    await refetchUsers();
  };

  // Create new location record
  const createLocationRecord = async (record: Omit<LocationHistoryData, 'id' | 'user_email'>) => {
    setIsCreating(true);
    try {
      const { data, error } = await supabase
        .from('user_location_history')
        .insert(record)
        .select()
        .single();

      if (error) {
        console.error('Error creating location record:', error);
        return null;
      }

      await refetchLocationData();
      await refetchUsers();
      return data;
    } catch (error) {
      console.error('Error:', error);
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  // Update location record
  const updateLocationRecord = async (id: string, updates: Partial<Omit<LocationHistoryData, 'id' | 'user_email'>>) => {
    setIsUpdating(true);
    try {
      const { data, error } = await supabase
        .from('user_location_history')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating location record:', error);
        return null;
      }

      await refetchLocationData();
      return data;
    } catch (error) {
      console.error('Error:', error);
      return null;
    } finally {
      setIsUpdating(false);
    }
  };

  // Delete location record
  const deleteLocationRecord = async (id: string) => {
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('user_location_history')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting location record:', error);
        return false;
      }

      await refetchLocationData();
      return true;
    } catch (error) {
      console.error('Error:', error);
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  // Delete all records for a user
  const deleteUserLocationHistory = async (userId: string) => {
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from('user_location_history')
        .delete()
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting user location history:', error);
        return false;
      }

      await refetchLocationData();
      await refetchUsers();
      return true;
    } catch (error) {
      console.error('Error:', error);
      return false;
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    locationData,
    users,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    fetchLocationData,
    fetchUsers,
    createLocationRecord,
    updateLocationRecord,
    deleteLocationRecord,
    deleteUserLocationHistory
  };
};
