import { useAuth } from "@/hooks/auth/useAuth";
import { useFlexibleDealsQuery, DealCollectionType } from "@/queries/useFlexibleDealsQuery";

export type { DealCollectionType };

export const useFlexibleDeals = (
  collectionType: DealCollectionType,
  options: {
    maxSections?: number;
    dealsPerSection?: number;
    userPreferences?: boolean;
  } = {}
) => {
  const { user } = useAuth();
  const {
    maxSections = 6,
    dealsPerSection = 10,
    userPreferences = true
  } = options;

  const { data: sections = [], isLoading, error } = useFlexibleDealsQuery(collectionType, {
    maxSections,
    dealsPerSection,
    userPreferences,
    userId: user?.id
  });

  const filteredSections = sections.filter(section => section.deals.length > 0);

  return {
    sections: filteredSections,
    isLoading,
    error,
    refetch: () => {
    }
  };
};
