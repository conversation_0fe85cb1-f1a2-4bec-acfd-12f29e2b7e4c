import { User } from "lucide-react";
import { format } from "date-fns";
import { it } from "date-fns/locale";
import ReactMarkdown from "react-markdown";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { MessageWithRole } from "@/types/conversations";
import { QRCodeSVG } from "qrcode.react";

interface AudioMessageProps {
  content: string;
}

interface UserMessageProps {
  message: MessageWithRole;
  isLoggedUsermessage: boolean;
  AudioMessage: React.FC<AudioMessageProps>;
  isBase64Audio: (content: string) => boolean;
}

const UserMessage: React.FC<UserMessageProps> = ({
  message,
  isLoggedUsermessage,
  AudioMessage,
  isBase64Audio,
}) => {
  return (
    <div className={`flex ${isLoggedUsermessage ? "justify-end" : "justify-start"}`}>
      <div
        className={`flex items-end gap-2 max-w-[80%] ${
          isLoggedUsermessage ? "flex-row-reverse" : "flex-row"
        }`}
      >
        {/* Avatar */}
        <Avatar className="h-8 w-8 bg-brand-primary/20 text-brand-primary">
          <AvatarFallback>
            <User className="h-4 w-4" />
          </AvatarFallback>
        </Avatar>

        {/* Message Content */}
        <div className="rounded-lg p-3 shadow-sm bg-brand-primary text-white rounded-tr-none">
          {/* Sender Name */}
          <div className="text-xs mb-1 font-medium text-white/80">
            {isLoggedUsermessage ? "Tu" : message.first_name}
          </div>

          {/* Message Content or Audio */}
          {isBase64Audio(message.content) ? (
            <AudioMessage content={message.content} />
          ) : (
            <div className="text-sm prose prose-sm dark:prose-invert max-w-none">
              <ReactMarkdown>{message.content}</ReactMarkdown>
            </div>
          )}

          {/* Timestamp */}
          <div className="text-xs mt-1 text-white/70">
            {format(new Date(message.created_at), "HH:mm", {
              locale: it,
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserMessage;
