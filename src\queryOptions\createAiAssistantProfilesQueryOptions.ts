import { supabase } from "@/integrations/supabase/client";
import { AIAssistantProfile } from "@/types/aiassistantprofile";
import { queryOptions } from "@tanstack/react-query";

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const fetchAssistantProfiles = async (): Promise<AIAssistantProfile[]> => {
  //  await sleep(3000);
  const { data, error } = await supabase
    .from("ai_assistant_profile")
    .select("*");
  if (error) {
    throw error;
  }

  return data || [];
};

function createAiAssistantProfilesQueryOptions() {
  return queryOptions({
    queryKey: ["ai_assistant_profiles"],
    queryFn: fetchAssistantProfiles,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

export default createAiAssistantProfilesQueryOptions;
